#!/bin/bash

# PISM-TYTO 登录测试脚本

echo "=== PISM-TYTO 登录测试 ==="
echo

# 测试服务器是否运行
echo "1. 检查服务器状态..."
if curl -s http://localhost:8080/login > /dev/null; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问"
    exit 1
fi

echo

# 测试正确的用户名和密码
echo "2. 测试正确的登录信息 (admin/123456)..."
RESPONSE=$(curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=admin&password=123456" \
    http://localhost:8080/login)

if echo "$RESPONSE" | grep -q '"code":200'; then
    echo "✅ 登录成功"
    echo "响应: $RESPONSE"
else
    echo "❌ 登录失败"
    echo "响应: $RESPONSE"
fi

echo

# 测试错误的用户名
echo "3. 测试错误的用户名 (wronguser/123456)..."
RESPONSE=$(curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=wronguser&password=123456" \
    http://localhost:8080/login)

if echo "$RESPONSE" | grep -q '"code":401'; then
    echo "✅ 正确拒绝错误用户名"
else
    echo "❌ 应该拒绝错误用户名"
    echo "响应: $RESPONSE"
fi

echo

# 测试错误的密码
echo "4. 测试错误的密码 (admin/wrongpass)..."
RESPONSE=$(curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=admin&password=wrongpass" \
    http://localhost:8080/login)

if echo "$RESPONSE" | grep -q '"code":401'; then
    echo "✅ 正确拒绝错误密码"
else
    echo "❌ 应该拒绝错误密码"
    echo "响应: $RESPONSE"
fi

echo
echo "=== 测试完成 ==="
