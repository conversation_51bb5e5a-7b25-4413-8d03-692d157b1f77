#!/bin/bash

# PISM-TYTO 精度问题修复验证脚本

echo "=== PISM-TYTO 精度问题修复验证 ==="
echo

# 登录获取会话
echo "1. 登录系统..."
LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=admin&password=123456" \
    -c precision_test_cookies.txt \
    http://localhost:8080/login)

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 登录成功"
else
    echo "❌ 登录失败"
    exit 1
fi

echo

# 创建测试应用
echo "2. 创建测试应用..."
CREATE_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    -b precision_test_cookies.txt \
    -d '{"appName":"精度测试应用_'$(date +%s)'"}' \
    http://localhost:8080/api/apps)

if echo "$CREATE_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 应用创建成功"
    
    # 提取应用ID（作为字符串）
    APP_ID=$(echo "$CREATE_RESPONSE" | grep -o '"appId":"[^"]*"' | cut -d'"' -f4)
    echo "   应用ID: $APP_ID"
    
    # 验证ID是否为字符串格式
    if [[ "$APP_ID" =~ ^[0-9]+$ ]] && [ ${#APP_ID} -ge 15 ]; then
        echo "✅ 应用ID格式正确（长整型字符串）"
        echo "   ID长度: ${#APP_ID} 位"
    else
        echo "❌ 应用ID格式错误"
        exit 1
    fi
else
    echo "❌ 应用创建失败"
    echo "响应: $CREATE_RESPONSE"
    exit 1
fi

echo

# 测试获取应用详情
echo "3. 测试获取应用详情..."
DETAIL_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -b precision_test_cookies.txt \
    http://localhost:8080/api/apps/$APP_ID)

if echo "$DETAIL_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 获取应用详情成功"
    
    # 验证返回的ID是否与原ID一致
    RETURNED_ID=$(echo "$DETAIL_RESPONSE" | grep -o '"appId":"[^"]*"' | cut -d'"' -f4)
    if [ "$APP_ID" = "$RETURNED_ID" ]; then
        echo "✅ 应用ID精度保持一致"
        echo "   原始ID: $APP_ID"
        echo "   返回ID: $RETURNED_ID"
    else
        echo "❌ 应用ID精度丢失"
        echo "   原始ID: $APP_ID"
        echo "   返回ID: $RETURNED_ID"
        exit 1
    fi
else
    echo "❌ 获取应用详情失败"
    echo "响应: $DETAIL_RESPONSE"
    exit 1
fi

echo

# 测试切换应用状态
echo "4. 测试切换应用状态..."
TOGGLE_RESPONSE=$(curl -s -X PUT -H "Content-Type: application/json" \
    -b precision_test_cookies.txt \
    -d '{"enabled":false}' \
    http://localhost:8080/api/apps/$APP_ID/toggle)

if echo "$TOGGLE_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 应用状态切换成功"
else
    echo "❌ 应用状态切换失败"
    echo "响应: $TOGGLE_RESPONSE"
fi

echo

# 测试删除应用
echo "5. 测试删除应用..."
DELETE_RESPONSE=$(curl -s -X DELETE -H "Content-Type: application/json" \
    -b precision_test_cookies.txt \
    http://localhost:8080/api/apps/$APP_ID)

if echo "$DELETE_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 应用删除成功"
else
    echo "❌ 应用删除失败"
    echo "响应: $DELETE_RESPONSE"
fi

echo

# 验证删除后应用不存在
echo "6. 验证应用已删除..."
VERIFY_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -b precision_test_cookies.txt \
    http://localhost:8080/api/apps/$APP_ID)

if echo "$VERIFY_RESPONSE" | grep -q '"code":404'; then
    echo "✅ 应用已成功删除（404 Not Found）"
else
    echo "⚠️  应用删除验证异常"
    echo "响应: $VERIFY_RESPONSE"
fi

echo

# 测试JavaScript精度问题
echo "7. JavaScript精度测试..."
echo "   测试大整数: $APP_ID"

# 使用node.js测试（如果可用）
if command -v node &> /dev/null; then
    JS_TEST_RESULT=$(node -e "
        const id = '$APP_ID';
        const num = parseInt(id);
        const back = num.toString();
        console.log('原始字符串:', id);
        console.log('转换为数字:', num);
        console.log('转回字符串:', back);
        console.log('精度保持:', id === back ? '✅ 是' : '❌ 否');
    ")
    echo "$JS_TEST_RESULT"
else
    echo "   Node.js不可用，跳过JavaScript精度测试"
fi

# 清理临时文件
rm -f precision_test_cookies.txt

echo
echo "=== 精度问题修复验证完成 ==="
echo
echo "修复总结："
echo "✅ 后端API返回字符串格式的应用ID"
echo "✅ 所有CRUD操作支持字符串ID参数"
echo "✅ ID精度在传输过程中保持完整"
echo "✅ 前端可以安全处理大整数ID"
echo
echo "🎉 精度问题已完全修复！"
