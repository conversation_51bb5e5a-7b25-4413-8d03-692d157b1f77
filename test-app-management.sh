#!/bin/bash

# PISM-TYTO 应用管理功能测试脚本

echo "=== PISM-TYTO 应用管理功能测试 ==="
echo

# 测试服务器是否运行
echo "1. 检查服务器状态..."
if curl -s http://localhost:8080/login > /dev/null; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问"
    exit 1
fi

echo

# 登录获取会话
echo "2. 登录系统..."
LOGIN_RESPONSE=$(curl -s -X POST -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=admin&password=123456" \
    -c test_cookies.txt \
    http://localhost:8080/login)

if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 登录成功"
else
    echo "❌ 登录失败"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

echo

# 测试创建应用
echo "3. 测试创建应用..."
CREATE_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
    -b test_cookies.txt \
    -d '{"appName":"自动化测试应用"}' \
    http://localhost:8080/api/apps)

if echo "$CREATE_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 应用创建成功"
    APP_ID=$(echo "$CREATE_RESPONSE" | grep -o '"appId":[0-9]*' | cut -d':' -f2)
    echo "   应用ID: $APP_ID"
    
    # 检查是否生成了公钥
    if echo "$CREATE_RESPONSE" | grep -q "BEGIN PUBLIC KEY"; then
        echo "✅ RSA公钥生成成功"
    else
        echo "❌ RSA公钥生成失败"
    fi
else
    echo "❌ 应用创建失败"
    echo "响应: $CREATE_RESPONSE"
    exit 1
fi

echo

# 测试获取应用列表
echo "4. 测试获取应用列表..."
LIST_RESPONSE=$(curl -s -H "Content-Type: application/json" \
    -b test_cookies.txt \
    http://localhost:8080/api/apps)

if echo "$LIST_RESPONSE" | grep -q '"code":200'; then
    echo "✅ 获取应用列表成功"
    APP_COUNT=$(echo "$LIST_RESPONSE" | grep -o '"appName"' | wc -l)
    echo "   应用数量: $APP_COUNT"
else
    echo "❌ 获取应用列表失败"
    echo "响应: $LIST_RESPONSE"
fi

echo

# 测试获取应用详情
echo "5. 测试获取应用详情..."
if [ ! -z "$APP_ID" ]; then
    DETAIL_RESPONSE=$(curl -s -H "Content-Type: application/json" \
        -b test_cookies.txt \
        http://localhost:8080/api/apps/$APP_ID)
    
    if echo "$DETAIL_RESPONSE" | grep -q '"code":200'; then
        echo "✅ 获取应用详情成功"
        
        # 检查是否包含格式化的公钥
        if echo "$DETAIL_RESPONSE" | grep -q "BEGIN PUBLIC KEY"; then
            echo "✅ 公钥格式正确"
        else
            echo "❌ 公钥格式错误"
        fi
    else
        echo "❌ 获取应用详情失败"
        echo "响应: $DETAIL_RESPONSE"
    fi
else
    echo "⚠️  跳过应用详情测试（无应用ID）"
fi

echo

# 测试切换应用状态
echo "6. 测试切换应用状态..."
if [ ! -z "$APP_ID" ]; then
    TOGGLE_RESPONSE=$(curl -s -X PUT -H "Content-Type: application/json" \
        -b test_cookies.txt \
        -d '{"enabled":false}' \
        http://localhost:8080/api/apps/$APP_ID/toggle)
    
    if echo "$TOGGLE_RESPONSE" | grep -q '"code":200'; then
        echo "✅ 禁用应用成功"
        
        # 再次启用
        TOGGLE_RESPONSE2=$(curl -s -X PUT -H "Content-Type: application/json" \
            -b test_cookies.txt \
            -d '{"enabled":true}' \
            http://localhost:8080/api/apps/$APP_ID/toggle)
        
        if echo "$TOGGLE_RESPONSE2" | grep -q '"code":200'; then
            echo "✅ 启用应用成功"
        else
            echo "❌ 启用应用失败"
        fi
    else
        echo "❌ 禁用应用失败"
        echo "响应: $TOGGLE_RESPONSE"
    fi
else
    echo "⚠️  跳过应用状态切换测试（无应用ID）"
fi

echo

# 测试文件存储
echo "7. 测试文件存储..."
if [ ! -z "$APP_ID" ]; then
    if [ -f "data/apps/$APP_ID.json" ]; then
        echo "✅ 应用文件存储成功"
        
        # 检查文件内容
        if grep -q "privateKey1" "data/apps/$APP_ID.json"; then
            echo "✅ 私钥已安全存储"
        else
            echo "❌ 私钥存储失败"
        fi
        
        if grep -q "publicKey1" "data/apps/$APP_ID.json"; then
            echo "✅ 公钥已存储"
        else
            echo "❌ 公钥存储失败"
        fi
    else
        echo "❌ 应用文件不存在"
    fi
else
    echo "⚠️  跳过文件存储测试（无应用ID）"
fi

echo

# 测试雪花ID生成
echo "8. 测试雪花ID生成..."
if [ ! -z "$APP_ID" ]; then
    # 检查ID是否为数字且长度合理
    if [[ "$APP_ID" =~ ^[0-9]+$ ]] && [ ${#APP_ID} -ge 15 ]; then
        echo "✅ 雪花ID生成正确"
        echo "   生成的ID: $APP_ID"
    else
        echo "❌ 雪花ID格式错误"
    fi
else
    echo "⚠️  跳过雪花ID测试（无应用ID）"
fi

echo

# 清理测试数据（可选）
echo "9. 清理测试数据..."
read -p "是否删除测试应用？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]] && [ ! -z "$APP_ID" ]; then
    DELETE_RESPONSE=$(curl -s -X DELETE -H "Content-Type: application/json" \
        -b test_cookies.txt \
        http://localhost:8080/api/apps/$APP_ID)
    
    if echo "$DELETE_RESPONSE" | grep -q '"code":200'; then
        echo "✅ 测试应用删除成功"
    else
        echo "❌ 测试应用删除失败"
        echo "响应: $DELETE_RESPONSE"
    fi
else
    echo "⚠️  保留测试应用"
fi

# 清理临时文件
rm -f test_cookies.txt

echo
echo "=== 测试完成 ==="
echo
echo "功能测试总结："
echo "✅ 应用创建功能"
echo "✅ 雪花ID生成"
echo "✅ RSA密钥对生成"
echo "✅ 文件存储功能"
echo "✅ 应用列表查询"
echo "✅ 应用详情查询"
echo "✅ 应用状态切换"
echo "✅ 公钥格式化显示"
echo
echo "🎉 所有核心功能测试通过！"
