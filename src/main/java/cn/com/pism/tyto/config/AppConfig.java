package cn.com.pism.tyto.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 应用配置类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@Component
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    /**
     * 用户配置
     */
    private User user = new User();

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    /**
     * 用户配置类
     */
    public static class User {
        /**
         * 用户名
         */
        private String username = "admin";

        /**
         * 密码
         */
        private String password = "123456";

        /**
         * 昵称
         */
        private String nickname = "系统管理员";

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        @Override
        public String toString() {
            return "User{" +
                    "username='" + username + '\'' +
                    ", password='[PROTECTED]'" +
                    ", nickname='" + nickname + '\'' +
                    '}';
        }
    }
}
