package cn.com.pism.tyto.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器，只拦截管理相关的功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，只拦截管理相关的接口
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 只拦截管理相关的路径
            SaRouter
                .match("/admin/**", "/manage/**", "/dashboard/**")    // 只拦截管理相关的路径
                .check(r -> StpUtil.checkLogin());        // 要执行的校验动作
        })).addPathPatterns("/**");
    }
}
