package cn.com.pism.tyto.config;

import cn.dev33.satoken.exception.NotLoginException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public Object handleNotLoginException(NotLoginException e, HttpServletRequest request, HttpServletResponse response) {
        // 判断是否为Ajax请求
        String requestedWith = request.getHeader("X-Requested-With");
        String accept = request.getHeader("Accept");
        
        // 如果是Ajax请求或者Accept包含application/json，返回JSON
        if ("XMLHttpRequest".equals(requestedWith) || 
            (accept != null && accept.contains("application/json"))) {
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 401);
            result.put("message", "请先登录");
            result.put("loginUrl", "/login");
            return result;
        } else {
            // 否则重定向到登录页面
            ModelAndView mv = new ModelAndView();
            mv.setViewName("redirect:/login");
            return mv;
        }
    }
}
