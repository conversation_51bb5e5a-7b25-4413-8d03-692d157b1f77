package cn.com.pism.tyto.entity;

import java.time.LocalDateTime;

/**
 * 应用实体类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
public class App {

    /**
     * 应用ID（雪花ID）
     */
    private Long appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 第一对RSA公钥
     */
    private String publicKey1;

    /**
     * 第一对RSA私钥
     */
    private String privateKey1;

    /**
     * 第二对RSA公钥
     */
    private String publicKey2;

    /**
     * 第二对RSA私钥
     */
    private String privateKey2;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 备注
     */
    private String remark;

    public App() {
    }

    public App(Long appId, String appName) {
        this.appId = appId;
        this.appName = appName;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.enabled = true;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPublicKey1() {
        return publicKey1;
    }

    public void setPublicKey1(String publicKey1) {
        this.publicKey1 = publicKey1;
    }

    public String getPrivateKey1() {
        return privateKey1;
    }

    public void setPrivateKey1(String privateKey1) {
        this.privateKey1 = privateKey1;
    }

    public String getPublicKey2() {
        return publicKey2;
    }

    public void setPublicKey2(String publicKey2) {
        this.publicKey2 = publicKey2;
    }

    public String getPrivateKey2() {
        return privateKey2;
    }

    public void setPrivateKey2(String privateKey2) {
        this.privateKey2 = privateKey2;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "App{" +
                "appId=" + appId +
                ", appName='" + appName + '\'' +
                ", publicKey1='[PROTECTED]'" +
                ", privateKey1='[PROTECTED]'" +
                ", publicKey2='[PROTECTED]'" +
                ", privateKey2='[PROTECTED]'" +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", enabled=" + enabled +
                ", remark='" + remark + '\'' +
                '}';
    }
}
