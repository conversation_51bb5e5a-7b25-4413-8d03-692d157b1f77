package cn.com.pism.tyto.util;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

/**
 * RSA密钥生成工具类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
public class RSAKeyGenerator {

    /**
     * RSA密钥长度
     */
    private static final int KEY_SIZE = 2048;

    /**
     * RSA密钥对
     */
    public static class RSAKeyPair {
        private final String publicKey;
        private final String privateKey;

        public RSAKeyPair(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

        /**
         * 获取格式化的公钥（PEM格式）
         */
        public String getFormattedPublicKey() {
            return "-----BEGIN PUBLIC KEY-----\n" +
                    formatKey(publicKey) +
                    "\n-----END PUBLIC KEY-----";
        }

        /**
         * 获取格式化的私钥（PEM格式）
         */
        public String getFormattedPrivateKey() {
            return "-----BEGIN PRIVATE KEY-----\n" +
                    formatKey(privateKey) +
                    "\n-----END PRIVATE KEY-----";
        }

        /**
         * 格式化密钥，每64个字符换行
         */
        private String formatKey(String key) {
            StringBuilder formatted = new StringBuilder();
            for (int i = 0; i < key.length(); i += 64) {
                if (i + 64 < key.length()) {
                    formatted.append(key, i, i + 64).append("\n");
                } else {
                    formatted.append(key.substring(i));
                }
            }
            return formatted.toString();
        }
    }

    /**
     * 生成RSA密钥对
     *
     * @return RSA密钥对
     * @throws NoSuchAlgorithmException 算法不存在异常
     */
    public static RSAKeyPair generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(KEY_SIZE);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        String publicKeyString = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        String privateKeyString = Base64.getEncoder().encodeToString(privateKey.getEncoded());

        return new RSAKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * 生成两对RSA密钥
     *
     * @return 包含两对密钥的数组
     * @throws NoSuchAlgorithmException 算法不存在异常
     */
    public static RSAKeyPair[] generateTwoKeyPairs() throws NoSuchAlgorithmException {
        return new RSAKeyPair[]{
                generateKeyPair(),
                generateKeyPair()
        };
    }
}
