package cn.com.pism.tyto.controller;

import cn.com.pism.tyto.entity.User;
import cn.com.pism.tyto.service.UserService;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 登录控制器
 */
@RestController
public class LoginController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 登录接口
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestParam String username, @RequestParam String password) {
        Map<String, Object> result = new HashMap<>();
        
        // 验证用户名密码
        if (userService.validateUser(username, password)) {
            // 登录成功，生成token
            StpUtil.login(username);
            
            result.put("code", 200);
            result.put("message", "登录成功");
            result.put("token", StpUtil.getTokenValue());
            result.put("user", userService.getUserInfo(username));
        } else {
            result.put("code", 401);
            result.put("message", "用户名或密码错误");
        }
        
        return result;
    }
    
    /**
     * 登出接口
     */
    @PostMapping("/logout")
    public Map<String, Object> logout() {
        Map<String, Object> result = new HashMap<>();
        
        StpUtil.logout();
        result.put("code", 200);
        result.put("message", "登出成功");
        
        return result;
    }
    
    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/user/info")
    public Map<String, Object> getUserInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否登录
            StpUtil.checkLogin();
            
            String username = StpUtil.getLoginIdAsString();
            User user = userService.getUserInfo(username);
            
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("user", user);
        } catch (Exception e) {
            result.put("code", 401);
            result.put("message", "未登录");
        }
        
        return result;
    }
    
    /**
     * 检查登录状态
     */
    @GetMapping("/user/isLogin")
    public Map<String, Object> isLogin() {
        Map<String, Object> result = new HashMap<>();
        
        boolean isLogin = StpUtil.isLogin();
        result.put("code", 200);
        result.put("isLogin", isLogin);
        
        if (isLogin) {
            result.put("username", StpUtil.getLoginIdAsString());
            result.put("tokenValue", StpUtil.getTokenValue());
        }
        
        return result;
    }
}
