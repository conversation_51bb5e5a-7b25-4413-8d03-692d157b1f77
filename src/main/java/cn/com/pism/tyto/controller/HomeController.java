package cn.com.pism.tyto.controller;

import cn.dev33.satoken.stp.StpUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 主页控制器
 */
@RestController
public class HomeController {
    
    /**
     * 首页 - 无需登录
     */
    @GetMapping("/")
    public Map<String, Object> home() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "欢迎访问 PISM-TYTO 系统");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 受保护的管理页面 - 需要登录
     */
    @GetMapping("/admin/dashboard")
    public Map<String, Object> adminDashboard() {
        Map<String, Object> result = new HashMap<>();

        // 检查登录状态
        StpUtil.checkLogin();

        String username = StpUtil.getLoginIdAsString();
        result.put("code", 200);
        result.put("message", "欢迎进入管理控制台，" + username);
        result.put("username", username);
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }
}
