package cn.com.pism.tyto.controller;

import cn.com.pism.tyto.entity.App;
import cn.com.pism.tyto.service.AppService;
import cn.com.pism.tyto.util.RSAKeyGenerator;
import cn.dev33.satoken.annotation.SaCheckLogin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 应用管理控制器
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@RequestMapping("/api/apps")
@SaCheckLogin
public class AppController {

    private static final Logger logger = LoggerFactory.getLogger(AppController.class);

    @Autowired
    private AppService appService;

    /**
     * 创建应用
     *
     * @param request 创建请求
     * @return 创建结果
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createApp(@RequestBody CreateAppRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证应用名称
            if (request.getAppName() == null || request.getAppName().trim().isEmpty()) {
                response.put("code", 400);
                response.put("message", "应用名称不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            String appName = request.getAppName().trim();
            
            // 检查应用名称是否已存在
            if (appService.existsByName(appName)) {
                response.put("code", 400);
                response.put("message", "应用名称已存在");
                return ResponseEntity.badRequest().body(response);
            }

            // 创建应用
            App app = appService.createApp(appName);

            // 构建响应数据（不包含私钥）
            Map<String, Object> appData = new HashMap<>();
            appData.put("appId", app.getAppId().toString()); // 转换为字符串避免精度丢失
            appData.put("appName", app.getAppName());
            appData.put("createTime", app.getCreateTime());
            appData.put("enabled", app.getEnabled());

            // 格式化公钥用于显示
            RSAKeyGenerator.RSAKeyPair keyPair1 = new RSAKeyGenerator.RSAKeyPair(app.getPublicKey1(), app.getPrivateKey1());
            RSAKeyGenerator.RSAKeyPair keyPair2 = new RSAKeyGenerator.RSAKeyPair(app.getPublicKey2(), app.getPrivateKey2());
            
            appData.put("publicKey1", keyPair1.getFormattedPublicKey());
            appData.put("publicKey2", keyPair2.getFormattedPublicKey());

            response.put("code", 200);
            response.put("message", "应用创建成功");
            response.put("data", appData);

            logger.info("应用创建成功: appId={}, appName={}", app.getAppId(), appName);
            return ResponseEntity.ok(response);

        } catch (NoSuchAlgorithmException e) {
            logger.error("RSA密钥生成失败: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "密钥生成失败");
            return ResponseEntity.internalServerError().body(response);
        } catch (IOException e) {
            logger.error("应用保存失败: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "应用保存失败");
            return ResponseEntity.internalServerError().body(response);
        } catch (Exception e) {
            logger.error("创建应用失败: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "创建应用失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取所有应用
     *
     * @return 应用列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllApps() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<App> apps = appService.findAll();
            
            // 构建响应数据（不包含私钥）
            List<Map<String, Object>> appList = apps.stream().map(app -> {
                Map<String, Object> appData = new HashMap<>();
                appData.put("appId", app.getAppId().toString()); // 转换为字符串避免精度丢失
                appData.put("appName", app.getAppName());
                appData.put("createTime", app.getCreateTime());
                appData.put("updateTime", app.getUpdateTime());
                appData.put("enabled", app.getEnabled());
                appData.put("remark", app.getRemark());
                return appData;
            }).toList();

            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", appList);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取应用列表失败: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "获取应用列表失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取应用详情
     *
     * @param appIdStr 应用ID字符串
     * @return 应用详情
     */
    @GetMapping("/{appId}")
    public ResponseEntity<Map<String, Object>> getAppById(@PathVariable("appId") String appIdStr) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 将字符串ID转换为Long
            Long appId = Long.parseLong(appIdStr);
            Optional<App> appOpt = appService.findById(appId);
            
            if (appOpt.isEmpty()) {
                response.put("code", 404);
                response.put("message", "应用不存在");
                return ResponseEntity.notFound().build();
            }

            App app = appOpt.get();
            
            // 构建响应数据（包含格式化的公钥）
            Map<String, Object> appData = new HashMap<>();
            appData.put("appId", app.getAppId().toString()); // 转换为字符串避免精度丢失
            appData.put("appName", app.getAppName());
            appData.put("createTime", app.getCreateTime());
            appData.put("updateTime", app.getUpdateTime());
            appData.put("enabled", app.getEnabled());
            appData.put("remark", app.getRemark());

            // 格式化公钥
            RSAKeyGenerator.RSAKeyPair keyPair1 = new RSAKeyGenerator.RSAKeyPair(app.getPublicKey1(), app.getPrivateKey1());
            RSAKeyGenerator.RSAKeyPair keyPair2 = new RSAKeyGenerator.RSAKeyPair(app.getPublicKey2(), app.getPrivateKey2());
            
            appData.put("publicKey1", keyPair1.getFormattedPublicKey());
            appData.put("publicKey2", keyPair2.getFormattedPublicKey());

            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", appData);
            return ResponseEntity.ok(response);

        } catch (NumberFormatException e) {
            logger.error("无效的应用ID格式: appId={}", appIdStr);
            response.put("code", 400);
            response.put("message", "无效的应用ID格式");
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.error("获取应用详情失败: appId={}, error={}", appIdStr, e.getMessage());
            response.put("code", 500);
            response.put("message", "获取应用详情失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 启用/禁用应用
     *
     * @param appIdStr 应用ID字符串
     * @param request 切换请求
     * @return 操作结果
     */
    @PutMapping("/{appId}/toggle")
    public ResponseEntity<Map<String, Object>> toggleApp(@PathVariable("appId") String appIdStr, @RequestBody ToggleAppRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 将字符串ID转换为Long
            Long appId = Long.parseLong(appIdStr);
            boolean success = appService.toggleApp(appId, request.getEnabled());
            
            if (success) {
                response.put("code", 200);
                response.put("message", request.getEnabled() ? "应用已启用" : "应用已禁用");
            } else {
                response.put("code", 404);
                response.put("message", "应用不存在");
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(response);

        } catch (NumberFormatException e) {
            logger.error("无效的应用ID格式: appId={}", appIdStr);
            response.put("code", 400);
            response.put("message", "无效的应用ID格式");
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.error("切换应用状态失败: appId={}, enabled={}, error={}", appIdStr, request.getEnabled(), e.getMessage());
            response.put("code", 500);
            response.put("message", "操作失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除应用
     *
     * @param appIdStr 应用ID字符串
     * @return 删除结果
     */
    @DeleteMapping("/{appId}")
    public ResponseEntity<Map<String, Object>> deleteApp(@PathVariable("appId") String appIdStr) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 将字符串ID转换为Long
            Long appId = Long.parseLong(appIdStr);
            boolean success = appService.deleteApp(appId);
            
            if (success) {
                response.put("code", 200);
                response.put("message", "应用删除成功");
            } else {
                response.put("code", 404);
                response.put("message", "应用不存在");
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(response);

        } catch (NumberFormatException e) {
            logger.error("无效的应用ID格式: appId={}", appIdStr);
            response.put("code", 400);
            response.put("message", "无效的应用ID格式");
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            logger.error("删除应用失败: appId={}, error={}", appIdStr, e.getMessage());
            response.put("code", 500);
            response.put("message", "删除失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 创建应用请求
     */
    public static class CreateAppRequest {
        private String appName;

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }
    }

    /**
     * 切换应用状态请求
     */
    public static class ToggleAppRequest {
        private Boolean enabled;

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }
    }
}
