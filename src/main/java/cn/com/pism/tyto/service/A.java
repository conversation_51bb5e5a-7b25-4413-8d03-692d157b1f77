package cn.com.pism.tyto.service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 25-08-09 18:23
 */
public class A {

    final MockDataService mockDataService;

    public A() {
        this.mockDataService = new MockDataService();
    }

    public MockDataService getMockDataService() {
        return mockDataService;
    }

    public void move(Long mainId, MrsConfigMoveAction action) {
        Long configId = action.getId();
        Long targetPrevId = action.getPrevId();

        // 1. 一次性查询该游戏下所有配置
        MerchantRecycleSeatConfigEntity moveConfig = mockDataService.getById(configId);
        if (moveConfig == null || !moveConfig.getMainId().equals(mainId)) {
            throw new RuntimeException("配置不存在或无权限操作");
        }

        Long gameId = moveConfig.getGameId();
        List<MerchantRecycleSeatConfigEntity> allConfigs = mockDataService.listByMainIdAndGameId(mainId, gameId);

        if (allConfigs.size() <= 1) {
            return;
        }

        // 2. 构建节点映射和链表关系
        Map<Long, MerchantRecycleSeatConfigEntity> configMap = allConfigs.stream()
            .collect(Collectors.toMap(MerchantRecycleSeatConfigEntity::getId, Function.identity()));
        
        // 找到当前链表的头节点
        MerchantRecycleSeatConfigEntity head = allConfigs.stream()
            .filter(config -> config.getPrevId() == null)
            .findFirst().orElse(null);

        // 3. 构建有序链表
        List<MerchantRecycleSeatConfigEntity> orderedConfigs = new ArrayList<>();
        MerchantRecycleSeatConfigEntity current = head;
        Set<Long> visited = new HashSet<>();
        
        while (current != null && !visited.contains(current.getId())) {
            visited.add(current.getId());
            orderedConfigs.add(current);
            Long nextId = findNextNodeId(allConfigs, current.getId());
            current = nextId != null ? configMap.get(nextId) : null;
        }

        // 4. 找到要移动节点的当前位置
        int moveIndex = -1;
        for (int i = 0; i < orderedConfigs.size(); i++) {
            if (orderedConfigs.get(i).getId().equals(configId)) {
                moveIndex = i;
                break;
            }
        }

        if (moveIndex == -1) {
            throw new RuntimeException("配置节点不在链表中");
        }

        // 5. 计算新位置
        int targetIndex;
        if (targetPrevId == null) {
            targetIndex = 0; // 移动到首位
        } else {
            targetIndex = -1;
            for (int i = 0; i < orderedConfigs.size(); i++) {
                if (orderedConfigs.get(i).getId().equals(targetPrevId)) {
                    targetIndex = i + 1; // 移动到目标节点后面
                    break;
                }
            }
            if (targetIndex == -1) {
                throw new RuntimeException("目标位置不存在");
            }
        }

        // 6. 如果位置没有变化，直接返回
        if (moveIndex == targetIndex || (moveIndex == targetIndex - 1 && targetIndex > 0)) {
            return;
        }

        // 7. 重新排列链表
        MerchantRecycleSeatConfigEntity moveNode = orderedConfigs.remove(moveIndex);
        if (targetIndex > moveIndex) {
            targetIndex--; // 因为移除了一个元素，目标位置需要调整
        }
        orderedConfigs.add(targetIndex, moveNode);

        // 8. 批量更新prevId
        List<MerchantRecycleSeatConfigEntity> updateList = new ArrayList<>();
        for (int i = 0; i < orderedConfigs.size(); i++) {
            MerchantRecycleSeatConfigEntity config = orderedConfigs.get(i);
            Long newPrevId = i == 0 ? null : orderedConfigs.get(i - 1).getId();
            
            if (!Objects.equals(config.getPrevId(), newPrevId)) {
                config.setPrevId(newPrevId);
                updateList.add(config);
            }
        }

        // 9. 批量更新模拟数据
        if (!updateList.isEmpty()) {
            mockDataService.updateBatch(updateList);
        }
    }

    /**
     * 查找指定节点的下一个节点ID
     */
    private Long findNextNodeId(List<MerchantRecycleSeatConfigEntity> allConfigs, Long currentId) {
        return allConfigs.stream()
            .filter(config -> Objects.equals(config.getPrevId(), currentId))
            .map(MerchantRecycleSeatConfigEntity::getId)
            .findFirst()
            .orElse(null);
    }
}
