# 链表移动功能重构说明

## 概述

本次重构将原有的数据库操作替换为对List的操作，使用模拟数据服务来验证move方法的功能。重构后的代码不依赖Spring容器，可以独立运行和测试。

## 重构内容

### 1. 核心类修改

#### A.java
- **原有功能**: 使用数据库操作（`this.getById()`, `this.list()`, `this.updateBatchById()`）
- **重构后**: 使用MockDataService进行List操作
- **主要变化**:
  - 移除数据库依赖
  - 注入MockDataService实例
  - 保持原有的链表移动逻辑不变

#### MockDataService.java (新增)
- **功能**: 模拟数据库操作的服务类
- **特性**:
  - 使用List<MerchantRecycleSeatConfigEntity>存储数据
  - 提供getById()、listByMainIdAndGameId()、updateBatch()方法
  - 包含调试功能printChainStatus()
  - 初始化测试数据

### 2. 测试类

#### MoveTestMain.java
- **功能**: 基础功能测试
- **测试场景**:
  - 将节点移动到首位
  - 将节点移动到指定位置
  - 错误处理测试（不存在的节点、权限验证等）

#### ExtendedMoveTest.java
- **功能**: 扩展测试场景
- **测试场景**:
  - 单个节点情况
  - 两个节点情况
  - 复杂移动序列
  - 边界情况测试

## 数据结构

### 链表结构
使用prevId字段构建单向链表：
```
节点1(prevId=null) -> 节点2(prevId=1) -> 节点3(prevId=2) -> 节点4(prevId=3) -> 节点5(prevId=4)
```

### 测试数据
- **mainId**: 100L
- **gameId**: 200L
- **节点ID**: 1L, 2L, 3L, 4L, 5L
- **用户ID**: 1001L, 1002L, 1003L, 1004L, 1005L

## 运行方式

### 编译
```bash
cd /Users/<USER>/dev/project/pism/pism-tyto
javac src/main/java/cn/com/pism/tyto/service/MerchantRecycleSeatConfigEntity.java \
      src/main/java/cn/com/pism/tyto/service/MrsConfigMoveAction.java \
      src/main/java/cn/com/pism/tyto/service/MockDataService.java \
      src/main/java/cn/com/pism/tyto/service/A.java \
      src/main/java/cn/com/pism/tyto/service/MoveTestMain.java \
      src/main/java/cn/com/pism/tyto/service/ExtendedMoveTest.java
```

### 运行基础测试
```bash
java -cp src/main/java cn.com.pism.tyto.service.MoveTestMain
```

### 运行扩展测试
```bash
java -cp src/main/java cn.com.pism.tyto.service.ExtendedMoveTest
```

## 测试结果验证

### 基础测试结果
1. ✅ 节点移动到首位：3 -> 1 -> 2 -> 4 -> 5
2. ✅ 节点移动到指定位置：3 -> 5 -> 1 -> 2 -> 4
3. ✅ 复杂移动序列：3 -> 5 -> 2 -> 1 -> 4
4. ✅ 错误处理：正确抛出异常

### 扩展测试结果
1. ✅ 边界情况：相同位置移动被正确忽略
2. ✅ 复杂序列：多步移动操作正确执行
3. ✅ 链表完整性：每次操作后链表结构保持正确

## 核心算法

### move方法逻辑
1. **验证权限**: 检查配置是否存在且属于指定主账号
2. **查询数据**: 获取同游戏下的所有配置
3. **构建链表**: 根据prevId构建有序链表
4. **计算位置**: 确定移动节点的当前位置和目标位置
5. **位置检查**: 如果位置无变化则直接返回
6. **重新排列**: 在内存中重新排列链表
7. **批量更新**: 更新需要修改prevId的节点

### 关键特性
- **原子性**: 批量更新保证数据一致性
- **效率**: 只更新需要修改的节点
- **安全性**: 完整的权限和边界检查
- **可维护性**: 清晰的步骤划分和注释

## 优势

1. **独立性**: 不依赖Spring容器，可独立测试
2. **可测试性**: 使用模拟数据，测试更加可控
3. **可维护性**: 代码结构清晰，易于理解和修改
4. **可扩展性**: 易于添加新的测试场景
5. **性能**: 内存操作比数据库操作更快

## 注意事项

1. **数据隔离**: 不同游戏的配置数据相互隔离
2. **链表完整性**: 确保每次操作后链表结构正确
3. **并发安全**: 当前实现为单线程，多线程环境需要额外考虑
4. **数据持久化**: 实际使用时需要将List操作替换回数据库操作
