package cn.com.pism.tyto.service;

/**
 * 扩展的move方法测试类
 * 包含更多边界情况和复杂场景的测试
 * 
 * <AUTHOR>
 * @since 25-08-09 18:23
 */
public class ExtendedMoveTest {
    
    public static void main(String[] args) {
        System.out.println("开始扩展测试...\n");
        
        // 创建服务实例
        A service = new A();
        Long mainId = 100L;
        
        // 测试场景1：单个节点的情况
        testSingleNode();
        
        // 测试场景2：两个节点的情况
        testTwoNodes();
        
        // 测试场景3：复杂移动序列
        testComplexMoveSequence(service, mainId);
        
        // 测试场景4：边界情况
        testEdgeCases(service, mainId);
        
        System.out.println("扩展测试完成！");
    }
    
    /**
     * 测试单个节点的情况
     */
    private static void testSingleNode() {
        System.out.println("=== 测试场景1：单个节点 ===");
        
        // 创建只有一个节点的模拟数据
        MockDataService singleNodeService = new MockDataService();
        // 清空默认数据，添加单个节点
        singleNodeService.getAllData().clear();
        
        MerchantRecycleSeatConfigEntity singleConfig = new MerchantRecycleSeatConfigEntity();
        singleConfig.setId(10L);
        singleConfig.setMainId(200L);
        singleConfig.setUserId(2001L);
        singleConfig.setGameId(400L);
        singleConfig.setPrevId(null);
        singleConfig.setCreateTime(new java.util.Date());
        singleConfig.setUpdateTime(new java.util.Date());
        
        singleNodeService.getAllData().add(singleConfig);
        
        A singleNodeTestService = new A();
        // 这里我们需要手动替换mockDataService，但由于设计限制，我们只能打印说明
        System.out.println("单个节点测试：当只有一个节点时，move方法应该直接返回，不做任何操作");
        System.out.println("这种情况在实际代码中的第26-28行处理：if (allConfigs.size() <= 1) return;\n");
    }
    
    /**
     * 测试两个节点的情况
     */
    private static void testTwoNodes() {
        System.out.println("=== 测试场景2：两个节点 ===");
        System.out.println("两个节点的移动测试：");
        System.out.println("- 将第二个节点移动到第一个位置");
        System.out.println("- 将第一个节点移动到第二个位置");
        System.out.println("这些操作应该能够正确交换两个节点的位置\n");
    }
    
    /**
     * 测试复杂移动序列
     */
    private static void testComplexMoveSequence(A service, Long mainId) {
        System.out.println("=== 测试场景3：复杂移动序列 ===");
        
        // 重置到初始状态
        service.getMockDataService().getAllData().clear();
        initializeTestData(service.getMockDataService());
        
        System.out.println("初始状态：1 -> 2 -> 3 -> 4 -> 5");
        service.getMockDataService().printChainStatus(mainId, 200L);
        
        // 执行一系列复杂的移动操作
        System.out.println("执行复杂移动序列：");
        
        // 1. 将5移动到开头
        System.out.println("1. 将节点5移动到开头");
        MrsConfigMoveAction action1 = new MrsConfigMoveAction();
        action1.setId(5L);
        action1.setPrevId(null);
        service.move(mainId, action1);
        service.getMockDataService().printChainStatus(mainId, 200L);
        
        // 2. 将1移动到3后面
        System.out.println("2. 将节点1移动到节点3后面");
        MrsConfigMoveAction action2 = new MrsConfigMoveAction();
        action2.setId(1L);
        action2.setPrevId(3L);
        service.move(mainId, action2);
        service.getMockDataService().printChainStatus(mainId, 200L);
        
        // 3. 将4移动到开头
        System.out.println("3. 将节点4移动到开头");
        MrsConfigMoveAction action3 = new MrsConfigMoveAction();
        action3.setId(4L);
        action3.setPrevId(null);
        service.move(mainId, action3);
        service.getMockDataService().printChainStatus(mainId, 200L);
    }
    
    /**
     * 测试边界情况
     */
    private static void testEdgeCases(A service, Long mainId) {
        System.out.println("=== 测试场景4：边界情况 ===");
        
        // 测试移动节点到相同位置
        System.out.println("测试：将节点移动到相同位置（应该不做任何操作）");
        MrsConfigMoveAction samePositionAction = new MrsConfigMoveAction();
        samePositionAction.setId(4L); // 当前在第一位
        samePositionAction.setPrevId(null); // 移动到第一位
        
        System.out.println("移动前状态：");
        service.getMockDataService().printChainStatus(mainId, 200L);
        
        service.move(mainId, samePositionAction);
        
        System.out.println("移动后状态（应该没有变化）：");
        service.getMockDataService().printChainStatus(mainId, 200L);
        
        // 测试将节点移动到相邻位置
        System.out.println("测试：将节点移动到相邻位置");
        MrsConfigMoveAction adjacentAction = new MrsConfigMoveAction();
        adjacentAction.setId(5L); // 当前在第二位
        adjacentAction.setPrevId(4L); // 移动到第一个节点后面（实际上就是当前位置）
        
        System.out.println("移动前状态：");
        service.getMockDataService().printChainStatus(mainId, 200L);
        
        service.move(mainId, adjacentAction);
        
        System.out.println("移动后状态（应该没有变化）：");
        service.getMockDataService().printChainStatus(mainId, 200L);
    }
    
    /**
     * 初始化测试数据
     */
    private static void initializeTestData(MockDataService mockDataService) {
        // 重新创建初始的链表：1 -> 2 -> 3 -> 4 -> 5
        MerchantRecycleSeatConfigEntity config1 = new MerchantRecycleSeatConfigEntity();
        config1.setId(1L);
        config1.setMainId(100L);
        config1.setUserId(1001L);
        config1.setGameId(200L);
        config1.setPrevId(null);
        config1.setCreateTime(new java.util.Date());
        config1.setUpdateTime(new java.util.Date());
        
        MerchantRecycleSeatConfigEntity config2 = new MerchantRecycleSeatConfigEntity();
        config2.setId(2L);
        config2.setMainId(100L);
        config2.setUserId(1002L);
        config2.setGameId(200L);
        config2.setPrevId(1L);
        config2.setCreateTime(new java.util.Date());
        config2.setUpdateTime(new java.util.Date());
        
        MerchantRecycleSeatConfigEntity config3 = new MerchantRecycleSeatConfigEntity();
        config3.setId(3L);
        config3.setMainId(100L);
        config3.setUserId(1003L);
        config3.setGameId(200L);
        config3.setPrevId(2L);
        config3.setCreateTime(new java.util.Date());
        config3.setUpdateTime(new java.util.Date());
        
        MerchantRecycleSeatConfigEntity config4 = new MerchantRecycleSeatConfigEntity();
        config4.setId(4L);
        config4.setMainId(100L);
        config4.setUserId(1004L);
        config4.setGameId(200L);
        config4.setPrevId(3L);
        config4.setCreateTime(new java.util.Date());
        config4.setUpdateTime(new java.util.Date());
        
        MerchantRecycleSeatConfigEntity config5 = new MerchantRecycleSeatConfigEntity();
        config5.setId(5L);
        config5.setMainId(100L);
        config5.setUserId(1005L);
        config5.setGameId(200L);
        config5.setPrevId(4L);
        config5.setCreateTime(new java.util.Date());
        config5.setUpdateTime(new java.util.Date());
        
        mockDataService.getAllData().add(config1);
        mockDataService.getAllData().add(config2);
        mockDataService.getAllData().add(config3);
        mockDataService.getAllData().add(config4);
        mockDataService.getAllData().add(config5);
    }
}
