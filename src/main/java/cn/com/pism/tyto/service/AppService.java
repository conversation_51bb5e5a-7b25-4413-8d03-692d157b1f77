package cn.com.pism.tyto.service;

import cn.com.pism.tyto.entity.App;
import cn.com.pism.tyto.util.RSAKeyGenerator;
import cn.com.pism.tyto.util.SnowflakeIdGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 应用服务类
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class AppService {

    private static final Logger logger = LoggerFactory.getLogger(AppService.class);

    /**
     * 应用数据存储目录
     */
    private static final String DATA_DIR = "data/apps";

    /**
     * JSON文件扩展名
     */
    private static final String JSON_EXTENSION = ".json";

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    private final ObjectMapper objectMapper;

    public AppService() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        
        // 确保数据目录存在
        try {
            Files.createDirectories(Paths.get(DATA_DIR));
        } catch (IOException e) {
            logger.error("创建数据目录失败: {}", e.getMessage());
        }
    }

    /**
     * 创建应用
     *
     * @param appName 应用名称
     * @return 创建的应用
     * @throws NoSuchAlgorithmException RSA密钥生成异常
     * @throws IOException 文件操作异常
     */
    public App createApp(String appName) throws NoSuchAlgorithmException, IOException {
        // 检查应用名称是否已存在
        if (existsByName(appName)) {
            throw new IllegalArgumentException("应用名称已存在: " + appName);
        }

        // 生成雪花ID
        Long appId = snowflakeIdGenerator.nextId();

        // 创建应用对象
        App app = new App(appId, appName);

        // 生成两对RSA密钥
        RSAKeyGenerator.RSAKeyPair[] keyPairs = RSAKeyGenerator.generateTwoKeyPairs();
        
        app.setPublicKey1(keyPairs[0].getPublicKey());
        app.setPrivateKey1(keyPairs[0].getPrivateKey());
        app.setPublicKey2(keyPairs[1].getPublicKey());
        app.setPrivateKey2(keyPairs[1].getPrivateKey());

        // 保存到文件
        saveToFile(app);

        logger.info("创建应用成功: appId={}, appName={}", appId, appName);
        return app;
    }

    /**
     * 根据应用ID查找应用
     *
     * @param appId 应用ID
     * @return 应用信息
     */
    public Optional<App> findById(Long appId) {
        try {
            Path filePath = getFilePath(appId);
            if (!Files.exists(filePath)) {
                return Optional.empty();
            }

            String json = Files.readString(filePath);
            App app = objectMapper.readValue(json, App.class);
            return Optional.of(app);
        } catch (IOException e) {
            logger.error("读取应用文件失败: appId={}, error={}", appId, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 根据应用名称查找应用
     *
     * @param appName 应用名称
     * @return 应用信息
     */
    public Optional<App> findByName(String appName) {
        List<App> allApps = findAll();
        return allApps.stream()
                .filter(app -> app.getAppName().equals(appName))
                .findFirst();
    }

    /**
     * 检查应用名称是否存在
     *
     * @param appName 应用名称
     * @return 是否存在
     */
    public boolean existsByName(String appName) {
        return findByName(appName).isPresent();
    }

    /**
     * 获取所有应用
     *
     * @return 应用列表
     */
    public List<App> findAll() {
        List<App> apps = new ArrayList<>();
        try {
            Path dataPath = Paths.get(DATA_DIR);
            if (!Files.exists(dataPath)) {
                return apps;
            }

            Files.list(dataPath)
                    .filter(path -> path.toString().endsWith(JSON_EXTENSION))
                    .forEach(path -> {
                        try {
                            String json = Files.readString(path);
                            App app = objectMapper.readValue(json, App.class);
                            apps.add(app);
                        } catch (IOException e) {
                            logger.error("读取应用文件失败: path={}, error={}", path, e.getMessage());
                        }
                    });
        } catch (IOException e) {
            logger.error("读取应用目录失败: {}", e.getMessage());
        }

        return apps;
    }

    /**
     * 更新应用
     *
     * @param app 应用信息
     * @throws IOException 文件操作异常
     */
    public void updateApp(App app) throws IOException {
        app.setUpdateTime(LocalDateTime.now());
        saveToFile(app);
        logger.info("更新应用成功: appId={}, appName={}", app.getAppId(), app.getAppName());
    }

    /**
     * 删除应用
     *
     * @param appId 应用ID
     * @return 是否删除成功
     */
    public boolean deleteApp(Long appId) {
        try {
            Path filePath = getFilePath(appId);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                logger.info("删除应用成功: appId={}", appId);
                return true;
            }
            return false;
        } catch (IOException e) {
            logger.error("删除应用失败: appId={}, error={}", appId, e.getMessage());
            return false;
        }
    }

    /**
     * 启用/禁用应用
     *
     * @param appId   应用ID
     * @param enabled 是否启用
     * @return 是否操作成功
     */
    public boolean toggleApp(Long appId, boolean enabled) {
        try {
            Optional<App> appOpt = findById(appId);
            if (appOpt.isPresent()) {
                App app = appOpt.get();
                app.setEnabled(enabled);
                updateApp(app);
                return true;
            }
            return false;
        } catch (IOException e) {
            logger.error("切换应用状态失败: appId={}, enabled={}, error={}", appId, enabled, e.getMessage());
            return false;
        }
    }

    /**
     * 保存应用到文件
     *
     * @param app 应用信息
     * @throws IOException 文件操作异常
     */
    private void saveToFile(App app) throws IOException {
        Path filePath = getFilePath(app.getAppId());
        String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(app);
        Files.writeString(filePath, json);
    }

    /**
     * 获取应用文件路径
     *
     * @param appId 应用ID
     * @return 文件路径
     */
    private Path getFilePath(Long appId) {
        return Paths.get(DATA_DIR, appId + JSON_EXTENSION);
    }
}
