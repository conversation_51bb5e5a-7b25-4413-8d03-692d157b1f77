package cn.com.pism.tyto.service;

/**
 * 测试move方法的主类
 * 不使用Spring容器，直接创建对象进行测试
 * 
 * <AUTHOR>
 * @since 25-08-09 18:23
 */
public class MoveTestMain {
    
    public static void main(String[] args) {
        System.out.println("开始测试move方法...\n");
        
        // 创建服务实例
        A service = new A();
        
        // 测试数据：mainId=100L, gameId=200L
        // 初始链表：1 -> 2 -> 3 -> 4 -> 5
        Long mainId = 100L;
        
        // 打印初始状态
        System.out.println("=== 初始状态 ===");
        service.mockDataService.printChainStatus(mainId, 200L);
        
        // 测试1：将节点3移动到首位 (prevId=null)
        System.out.println("测试1：将节点3移动到首位");
        MrsConfigMoveAction action1 = new MrsConfigMoveAction();
        action1.setId(3L);
        action1.setPrevId(null);
        
        try {
            service.move(mainId, action1);
            System.out.println("移动成功！");
            service.mockDataService.printChainStatus(mainId, 200L);
        } catch (Exception e) {
            System.out.println("移动失败：" + e.getMessage());
        }
        
        // 测试2：将节点5移动到节点1后面
        System.out.println("测试2：将节点5移动到节点3后面");
        MrsConfigMoveAction action2 = new MrsConfigMoveAction();
        action2.setId(5L);
        action2.setPrevId(3L);
        
        try {
            service.move(mainId, action2);
            System.out.println("移动成功！");
            service.mockDataService.printChainStatus(mainId, 200L);
        } catch (Exception e) {
            System.out.println("移动失败：" + e.getMessage());
        }
        
        // 测试3：将节点1移动到节点2后面
        System.out.println("测试3：将节点1移动到节点2后面");
        MrsConfigMoveAction action3 = new MrsConfigMoveAction();
        action3.setId(1L);
        action3.setPrevId(2L);
        
        try {
            service.move(mainId, action3);
            System.out.println("移动成功！");
            service.mockDataService.printChainStatus(mainId, 200L);
        } catch (Exception e) {
            System.out.println("移动失败：" + e.getMessage());
        }
        
        // 测试4：将节点4移动到末尾（移动到当前最后一个节点后面）
        System.out.println("测试4：将节点4移动到节点1后面（末尾）");
        MrsConfigMoveAction action4 = new MrsConfigMoveAction();
        action4.setId(4L);
        action4.setPrevId(1L);
        
        try {
            service.move(mainId, action4);
            System.out.println("移动成功！");
            service.mockDataService.printChainStatus(mainId, 200L);
        } catch (Exception e) {
            System.out.println("移动失败：" + e.getMessage());
        }
        
        // 测试5：错误测试 - 移动不存在的节点
        System.out.println("测试5：错误测试 - 移动不存在的节点");
        MrsConfigMoveAction action5 = new MrsConfigMoveAction();
        action5.setId(999L); // 不存在的ID
        action5.setPrevId(1L);
        
        try {
            service.move(mainId, action5);
            System.out.println("移动成功！");
        } catch (Exception e) {
            System.out.println("预期的错误：" + e.getMessage());
        }
        
        // 测试6：错误测试 - 移动到不存在的目标位置
        System.out.println("测试6：错误测试 - 移动到不存在的目标位置");
        MrsConfigMoveAction action6 = new MrsConfigMoveAction();
        action6.setId(2L);
        action6.setPrevId(999L); // 不存在的目标ID
        
        try {
            service.move(mainId, action6);
            System.out.println("移动成功！");
        } catch (Exception e) {
            System.out.println("预期的错误：" + e.getMessage());
        }
        
        // 测试7：权限测试 - 使用错误的mainId
        System.out.println("测试7：权限测试 - 使用错误的mainId");
        MrsConfigMoveAction action7 = new MrsConfigMoveAction();
        action7.setId(2L);
        action7.setPrevId(3L);
        
        try {
            service.move(999L, action7); // 错误的mainId
            System.out.println("移动成功！");
        } catch (Exception e) {
            System.out.println("预期的错误：" + e.getMessage());
        }
        
        // 最终状态
        System.out.println("=== 最终状态 ===");
        service.mockDataService.printChainStatus(mainId, 200L);
        
        System.out.println("测试完成！");
    }
}
