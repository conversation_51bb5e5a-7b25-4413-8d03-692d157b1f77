package cn.com.pism.tyto.service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 模拟数据库操作的服务类
 * 使用List来模拟数据库存储
 * 
 * <AUTHOR>
 * @since 25-08-09 18:23
 */
public class MockDataService {
    
    // 模拟数据库表，使用List存储数据
    private final List<MerchantRecycleSeatConfigEntity> dataList;
    
    public MockDataService() {
        this.dataList = new ArrayList<>();
        initMockData();
    }
    
    /**
     * 初始化模拟数据
     */
    private void initMockData() {
        // 创建测试数据 - 主账号1，游戏1的配置链表
        // 链表结构: 1 -> 2 -> 3 -> 4 -> 5
        
        MerchantRecycleSeatConfigEntity config1 = new MerchantRecycleSeatConfigEntity();
        config1.setId(1L);
        config1.setMainId(100L);
        config1.setUserId(1001L);
        config1.setGameId(200L);
        config1.setPrevId(null); // 头节点
        config1.setCreateTime(new Date());
        config1.setUpdateTime(new Date());
        
        MerchantRecycleSeatConfigEntity config2 = new MerchantRecycleSeatConfigEntity();
        config2.setId(2L);
        config2.setMainId(100L);
        config2.setUserId(1002L);
        config2.setGameId(200L);
        config2.setPrevId(1L);
        config2.setCreateTime(new Date());
        config2.setUpdateTime(new Date());
        
        MerchantRecycleSeatConfigEntity config3 = new MerchantRecycleSeatConfigEntity();
        config3.setId(3L);
        config3.setMainId(100L);
        config3.setUserId(1003L);
        config3.setGameId(200L);
        config3.setPrevId(2L);
        config3.setCreateTime(new Date());
        config3.setUpdateTime(new Date());
        
        MerchantRecycleSeatConfigEntity config4 = new MerchantRecycleSeatConfigEntity();
        config4.setId(4L);
        config4.setMainId(100L);
        config4.setUserId(1004L);
        config4.setGameId(200L);
        config4.setPrevId(3L);
        config4.setCreateTime(new Date());
        config4.setUpdateTime(new Date());
        
        MerchantRecycleSeatConfigEntity config5 = new MerchantRecycleSeatConfigEntity();
        config5.setId(5L);
        config5.setMainId(100L);
        config5.setUserId(1005L);
        config5.setGameId(200L);
        config5.setPrevId(4L);
        config5.setCreateTime(new Date());
        config5.setUpdateTime(new Date());
        
        dataList.add(config1);
        dataList.add(config2);
        dataList.add(config3);
        dataList.add(config4);
        dataList.add(config5);
        
        // 添加另一个游戏的数据用于测试隔离
        MerchantRecycleSeatConfigEntity config6 = new MerchantRecycleSeatConfigEntity();
        config6.setId(6L);
        config6.setMainId(100L);
        config6.setUserId(1006L);
        config6.setGameId(300L); // 不同的游戏ID
        config6.setPrevId(null);
        config6.setCreateTime(new Date());
        config6.setUpdateTime(new Date());
        
        dataList.add(config6);
    }
    
    /**
     * 根据ID查询单个配置
     */
    public MerchantRecycleSeatConfigEntity getById(Long id) {
        return dataList.stream()
                .filter(config -> Objects.equals(config.getId(), id))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据主账号ID和游戏ID查询配置列表
     */
    public List<MerchantRecycleSeatConfigEntity> listByMainIdAndGameId(Long mainId, Long gameId) {
        return dataList.stream()
                .filter(config -> Objects.equals(config.getMainId(), mainId) 
                        && Objects.equals(config.getGameId(), gameId))
                .collect(Collectors.toList());
    }
    
    /**
     * 批量更新配置
     */
    public void updateBatch(List<MerchantRecycleSeatConfigEntity> updateList) {
        for (MerchantRecycleSeatConfigEntity updateConfig : updateList) {
            // 找到对应的配置并更新
            for (int i = 0; i < dataList.size(); i++) {
                MerchantRecycleSeatConfigEntity existingConfig = dataList.get(i);
                if (Objects.equals(existingConfig.getId(), updateConfig.getId())) {
                    // 更新prevId和updateTime
                    existingConfig.setPrevId(updateConfig.getPrevId());
                    existingConfig.setUpdateTime(new Date());
                    break;
                }
            }
        }
    }
    
    /**
     * 获取所有数据（用于调试和验证）
     */
    public List<MerchantRecycleSeatConfigEntity> getAllData() {
        return new ArrayList<>(dataList);
    }
    
    /**
     * 打印当前链表状态（用于调试）
     */
    public void printChainStatus(Long mainId, Long gameId) {
        List<MerchantRecycleSeatConfigEntity> configs = listByMainIdAndGameId(mainId, gameId);
        
        System.out.println("=== 链表状态 (mainId=" + mainId + ", gameId=" + gameId + ") ===");
        
        // 构建节点映射
        Map<Long, MerchantRecycleSeatConfigEntity> configMap = configs.stream()
                .collect(Collectors.toMap(MerchantRecycleSeatConfigEntity::getId, config -> config));
        
        // 找到头节点
        MerchantRecycleSeatConfigEntity head = configs.stream()
                .filter(config -> config.getPrevId() == null)
                .findFirst().orElse(null);
        
        if (head == null) {
            System.out.println("未找到头节点");
            return;
        }
        
        // 遍历链表
        MerchantRecycleSeatConfigEntity current = head;
        Set<Long> visited = new HashSet<>();
        int position = 0;
        
        while (current != null && !visited.contains(current.getId())) {
            visited.add(current.getId());
            System.out.println("位置 " + position + ": ID=" + current.getId() + 
                    ", UserId=" + current.getUserId() + 
                    ", PrevId=" + current.getPrevId());
            
            // 找下一个节点
            Long currentId = current.getId();
            current = configs.stream()
                    .filter(config -> Objects.equals(config.getPrevId(), currentId))
                    .findFirst()
                    .orElse(null);
            position++;
        }
        
        System.out.println("=== 链表状态结束 ===\n");
    }
}
