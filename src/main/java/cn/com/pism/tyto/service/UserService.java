package cn.com.pism.tyto.service;

import cn.com.pism.tyto.config.AppConfig;
import cn.com.pism.tyto.entity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户服务类 - 基于内存的单用户管理
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private AppConfig appConfig;

    /**
     * 内存中的用户实例
     */
    private User currentUser;

    /**
     * 初始化用户
     */
    private void initUser() {
        if (currentUser == null) {
            AppConfig.User userConfig = appConfig.getUser();
            currentUser = new User(
                userConfig.getUsername(),
                userConfig.getPassword(),
                userConfig.getNickname()
            );
            logger.info("初始化单用户系统，用户名: {}", currentUser.getUsername());
        }
    }
    
    /**
     * 根据用户名查找用户
     */
    public User findByUsername(String username) {
        initUser();

        if (Objects.equals(currentUser.getUsername(), username)) {
            return currentUser;
        }

        return null;
    }

    /**
     * 验证用户登录
     */
    public boolean validateUser(String username, String password) {
        initUser();

        if (Objects.equals(currentUser.getUsername(), username) &&
            Objects.equals(currentUser.getPassword(), password)) {

            // 更新最后登录时间
            currentUser.setLastLoginTime(LocalDateTime.now());
            logger.info("用户登录成功: {}", username);
            return true;
        }

        logger.warn("用户登录失败: {}", username);
        return false;
    }

    /**
     * 获取用户信息（不包含密码）
     */
    public User getUserInfo(String username) {
        initUser();

        if (Objects.equals(currentUser.getUsername(), username)) {
            // 返回不包含密码的用户信息
            User userInfo = new User(currentUser.getUsername(), null, currentUser.getNickname());
            userInfo.setEnabled(currentUser.getEnabled());
            userInfo.setLastLoginTime(currentUser.getLastLoginTime());
            return userInfo;
        }

        return null;
    }

    /**
     * 获取当前用户信息
     *
     * @return 当前用户信息
     */
    public User getCurrentUser() {
        initUser();
        return currentUser;
    }

    /**
     * 检查用户是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    public boolean userExists(String username) {
        initUser();
        return Objects.equals(currentUser.getUsername(), username);
    }

    /**
     * 更新用户密码
     *
     * @param username 用户名
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    public boolean updatePassword(String username, String newPassword) {
        initUser();

        if (Objects.equals(currentUser.getUsername(), username)) {
            currentUser.setPassword(newPassword);
            logger.info("用户密码已更新: {}", username);
            return true;
        }

        return false;
    }

    /**
     * 更新用户昵称
     *
     * @param username 用户名
     * @param nickname 新昵称
     * @return 是否更新成功
     */
    public boolean updateNickname(String username, String nickname) {
        initUser();

        if (Objects.equals(currentUser.getUsername(), username)) {
            currentUser.setNickname(nickname);
            logger.info("用户昵称已更新: {} -> {}", username, nickname);
            return true;
        }

        return false;
    }

    /**
     * 获取用户配置信息（不包含密码）
     *
     * @return 用户配置信息
     */
    public AppConfig.User getUserConfig() {
        return appConfig.getUser();
    }
}
