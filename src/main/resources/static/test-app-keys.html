<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用密钥测试</title>
    <script src="/js/jquery.min.js"></script>
    <script src="/js/sweetalert2.min.js"></script>
    <link rel="stylesheet" href="/css/sweetalert2.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .app-list {
            margin-top: 20px;
        }
        .app-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .keys-display {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .key-box {
            background: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .key-content {
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>应用密钥测试页面</h1>
        
        <div>
            <button class="btn" onclick="login()">登录</button>
            <button class="btn" onclick="loadApps()">加载应用列表</button>
            <button class="btn" onclick="createTestApp()">创建测试应用</button>
        </div>

        <div class="app-list" id="appList">
            <h3>应用列表</h3>
            <div id="apps"></div>
        </div>

        <div class="keys-display" id="keysDisplay" style="display: none;">
            <h3>应用密钥</h3>
            <div id="appInfo"></div>
            <div id="keys"></div>
        </div>
    </div>

    <script>
        let currentToken = null;

        async function login() {
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'username=admin&password=123456'
                });
                
                const result = await response.json();
                if (result.code === 200) {
                    currentToken = result.token;
                    Swal.fire('成功', '登录成功', 'success');
                } else {
                    Swal.fire('错误', result.message, 'error');
                }
            } catch (error) {
                console.error('登录失败:', error);
                Swal.fire('错误', '登录失败', 'error');
            }
        }

        async function loadApps() {
            if (!currentToken) {
                Swal.fire('提示', '请先登录', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/apps');
                const result = await response.json();
                
                if (result.code === 200) {
                    displayApps(result.data);
                } else {
                    Swal.fire('错误', result.message, 'error');
                }
            } catch (error) {
                console.error('加载应用失败:', error);
                Swal.fire('错误', '加载应用失败', 'error');
            }
        }

        function displayApps(apps) {
            const appsDiv = document.getElementById('apps');
            appsDiv.innerHTML = '';
            
            apps.forEach(app => {
                const appDiv = document.createElement('div');
                appDiv.className = 'app-item';
                appDiv.innerHTML = `
                    <h4>${app.appName}</h4>
                    <p>应用ID: ${app.appId}</p>
                    <p>状态: ${app.enabled ? '启用' : '禁用'}</p>
                    <p>创建时间: ${new Date(app.createTime).toLocaleString()}</p>
                    <button class="btn" onclick="viewKeys(${app.appId}, '${app.appName}')">查看密钥</button>
                `;
                appsDiv.appendChild(appDiv);
            });
        }

        async function viewKeys(appId, appName) {
            try {
                const response = await fetch(`/api/apps/${appId}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    displayKeys(result.data);
                } else {
                    Swal.fire('错误', result.message, 'error');
                }
            } catch (error) {
                console.error('获取密钥失败:', error);
                Swal.fire('错误', '获取密钥失败', 'error');
            }
        }

        function displayKeys(app) {
            const keysDisplay = document.getElementById('keysDisplay');
            const appInfo = document.getElementById('appInfo');
            const keys = document.getElementById('keys');
            
            appInfo.innerHTML = `
                <h4>${app.appName}</h4>
                <p>应用ID: ${app.appId}</p>
            `;
            
            keys.innerHTML = `
                <div class="key-box">
                    <h5>公钥 1</h5>
                    <div class="key-content">${app.publicKey1}</div>
                    <button class="btn" onclick="copyToClipboard('${app.publicKey1.replace(/\n/g, '\\n')}', '公钥1')">复制</button>
                </div>
                <div class="key-box">
                    <h5>公钥 2</h5>
                    <div class="key-content">${app.publicKey2}</div>
                    <button class="btn" onclick="copyToClipboard('${app.publicKey2.replace(/\n/g, '\\n')}', '公钥2')">复制</button>
                </div>
            `;
            
            keysDisplay.style.display = 'block';
        }

        async function createTestApp() {
            if (!currentToken) {
                Swal.fire('提示', '请先登录', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/apps', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        appName: '测试应用_' + Date.now()
                    })
                });
                
                const result = await response.json();
                if (result.code === 200) {
                    Swal.fire('成功', '应用创建成功', 'success');
                    loadApps(); // 重新加载应用列表
                } else {
                    Swal.fire('错误', result.message, 'error');
                }
            } catch (error) {
                console.error('创建应用失败:', error);
                Swal.fire('错误', '创建应用失败', 'error');
            }
        }

        function copyToClipboard(text, keyName) {
            // 处理换行符
            const cleanText = text.replace(/\\n/g, '\n');
            navigator.clipboard.writeText(cleanText).then(() => {
                Swal.fire({
                    title: '复制成功',
                    text: `${keyName} 已复制到剪贴板`,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }).catch(() => {
                Swal.fire('错误', '复制失败', 'error');
            });
        }

        // 页面加载时自动登录
        window.onload = function() {
            login();
        };
    </script>
</body>
</html>
