/* 修复样式问题的CSS文件 */

/* 1. 修复输入框图标覆盖文字的问题 */
.relative input.pl-10 {
    padding-left: 2.5rem !important;
    padding-right: 0.75rem !important;
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

.absolute.inset-y-0.left-0.pl-3 {
    display: flex;
    align-items: center;
    pointer-events: none;
    z-index: 1;
}

/* 2. 移除所有不必要的下划线 */
a, button, .nav-link, .dropdown-item, .test-account {
    text-decoration: none !important;
}

a:hover, a:focus, a:active,
button:hover, button:focus, button:active,
.nav-link:hover, .nav-link:focus, .nav-link:active,
.dropdown-item:hover, .dropdown-item:focus, .dropdown-item:active,
.test-account:hover, .test-account:focus, .test-account:active {
    text-decoration: none !important;
}

/* 3. 确保按钮样式正确 */
button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
    text-decoration: none !important;
}

/* 4. 修复Alpine.js的x-show显示问题 */
[x-show] {
    transition: opacity 0.3s ease-in-out;
}

[x-show="false"] {
    opacity: 0;
    pointer-events: none;
}

[x-show="true"] {
    opacity: 1;
    pointer-events: auto;
}

/* 5. 修复下拉菜单样式 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 50;
    margin-top: 0.5rem;
    min-width: 12rem;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 0.5rem 0;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    color: #374151;
    text-decoration: none !important;
    transition: background-color 0.15s ease-in-out;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
    color: #111827;
    text-decoration: none !important;
}

/* 6. 修复侧边栏导航样式 */
.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6b7280;
    text-decoration: none !important;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.nav-link:hover {
    background-color: #f3f4f6;
    color: #111827;
    text-decoration: none !important;
}

.nav-link.active {
    background-color: #eff6ff;
    color: #1d4ed8;
    border-right: 2px solid #1d4ed8;
    text-decoration: none !important;
}

/* 7. 修复表单输入框样式 */
input[type="text"], 
input[type="password"], 
input[type="email"] {
    appearance: none;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #111827;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 100%;
}

input[type="text"]:focus, 
input[type="password"]:focus, 
input[type="email"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 8. 修复按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    border: none;
    text-decoration: none !important;
    padding: 0.75rem 1rem;
}

.btn:hover, .btn:focus {
    text-decoration: none !important;
}

/* 9. 修复卡片样式 */
.card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* 10. 修复图标定位 */
.icon-left {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 1;
}

/* 11. 确保所有交互元素没有下划线 */
* {
    text-decoration: none !important;
}

*:hover, *:focus, *:active {
    text-decoration: none !important;
}

/* 12. 修复Alpine.js过渡效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 13. 修复响应式隐藏/显示 */
@media (max-width: 767px) {
    .md\\:hidden {
        display: none !important;
    }
    .md\\:block {
        display: block !important;
    }
}

@media (min-width: 768px) {
    .md\\:block {
        display: block !important;
    }
    .md\\:hidden {
        display: none !important;
    }
    .md\\:translate-x-0 {
        transform: translateX(0px) !important;
    }
}

/* 14. 修复侧边栏过渡 */
.sidebar-transition {
    transition: transform 0.3s ease-in-out;
}

.-translate-x-full {
    transform: translateX(-100%);
}

.translate-x-0 {
    transform: translateX(0px);
}

/* 15. 修复玻璃效果 */
.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* 16. 修复渐变背景 */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 17. 修复悬停效果 */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
