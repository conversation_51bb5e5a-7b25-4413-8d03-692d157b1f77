<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PISM-TYTO 登录系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .user-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .hidden {
            display: none;
        }
        .test-accounts {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PISM-TYTO 登录系统</h1>
        
        <div id="loginSection">
            <div class="login-form">
                <h2>用户登录</h2>
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit">登录</button>
                </form>
                
                <div class="test-accounts">
                    <h4>测试账号:</h4>
                    <p>用户名: admin, 密码: 123456</p>
                    <p>用户名: user, 密码: 123456</p>
                    <p>用户名: test, 密码: 123456</p>
                </div>
            </div>
        </div>
        
        <div id="userSection" class="hidden">
            <h2>欢迎登录</h2>
            <div class="user-info" id="userInfo"></div>
            <button onclick="logout()">登出</button>
            <button onclick="testDashboard()">访问控制台</button>
        </div>
        
        <div id="message"></div>
    </div>

    <script>
        // 页面加载时检查登录状态
        window.onload = function() {
            checkLoginStatus();
        };

        // 检查登录状态
        function checkLoginStatus() {
            fetch('/user/isLogin')
                .then(response => response.json())
                .then(data => {
                    if (data.isLogin) {
                        showUserSection();
                        getUserInfo();
                    } else {
                        showLoginSection();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showLoginSection();
                });
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showMessage('登录成功！', 'success');
                    showUserSection();
                    getUserInfo();
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('登录失败，请重试', 'error');
            });
        });

        // 获取用户信息
        function getUserInfo() {
            fetch('/user/info')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        document.getElementById('userInfo').innerHTML = 
                            `<p><strong>用户名:</strong> ${data.user.username}</p>
                             <p><strong>昵称:</strong> ${data.user.nickname}</p>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        // 登出
        function logout() {
            fetch('/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showMessage('登出成功！', 'success');
                showLoginSection();
                document.getElementById('loginForm').reset();
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('登出失败', 'error');
            });
        }

        // 测试访问控制台
        function testDashboard() {
            fetch('/admin/dashboard')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showMessage(`控制台访问成功: ${data.message}`, 'success');
                    } else {
                        showMessage('访问控制台失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('访问控制台失败', 'error');
                });
        }

        // 显示登录区域
        function showLoginSection() {
            document.getElementById('loginSection').classList.remove('hidden');
            document.getElementById('userSection').classList.add('hidden');
        }

        // 显示用户区域
        function showUserSection() {
            document.getElementById('loginSection').classList.add('hidden');
            document.getElementById('userSection').classList.remove('hidden');
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
