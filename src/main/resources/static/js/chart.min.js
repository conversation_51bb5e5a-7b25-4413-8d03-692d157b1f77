/**
 * Minified by jsDelivr using Terser v5.39.0.
 * Original file: /npm/chart.js@4.5.0/dist/chart.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */
import{r as requestAnimFrame,a as resolve,e as effects,c as color,i as isObject,d as defaults,b as isArray,v as valueOrDefault,u as unlistenArrayEvents,l as listenArrayEvents,f as resolveObjectKey,g as isNumberFinite,h as defined,s as sign,j as createContext,k as isNullOrUndef,_ as _arrayUnique,t as toRadians,m as toPercentage,n as toDimension,T as TAU,o as formatNumber,p as _angleBetween,H as HALF_PI,P as PI,q as _getStartAndCountOfVisiblePoints,w as _scaleRangesChanged,x as isNumber,y as _parseObjectDataRadialScale,z as getRelativePosition,A as _rlookupByKey,B as _lookupByKey,C as _isPointInArea,D as getAngleFromPoint,E as toPadding,F as each,G as getMaximumSize,I as _getParentNode,J as readUsedSize,K as supportsEventListenerOptions,L as throttled,M as _isDomSupported,N as _factorize,O as finiteOrDefault,Q as callback,R as _addGrace,S as _limitValue,U as toDegrees,V as _measureText,W as _int16Range,X as _alignPixel,Y as clipArea,Z as renderText,$ as unclipArea,a0 as toFont,a1 as _toLeftRightCenter,a2 as _alignStartEnd,a3 as overrides,a4 as merge,a5 as _capitalize,a6 as descriptors,a7 as isFunction,a8 as _attachContext,a9 as _createResolver,aa as _descriptors,ab as mergeIf,ac as uid,ad as debounce,ae as retinaScale,af as clearCanvas,ag as setsEqual,ah as getDatasetClipArea,ai as _elementsEqual,aj as _isClickEvent,ak as _isBetween,al as _normalizeAngle,am as _readValueToProps,an as _updateBezierControlPoints,ao as _computeSegments,ap as _boundSegments,aq as _steppedInterpolation,ar as _bezierInterpolation,as as _pointInLine,at as _steppedLineTo,au as _bezierCurveTo,av as drawPoint,aw as addRoundedRectPath,ax as toTRBL,ay as toTRBLCorners,az as _boundSegment,aA as getRtlAdapter,aB as overrideTextDirection,aC as _textX,aD as restoreTextDirection,aE as drawPointLegend,aF as distanceBetweenPoints,aG as noop,aH as _setMinAndMaxByKey,aI as niceNum,aJ as almostWhole,aK as almostEquals,aL as _decimalPlaces,aM as Ticks,aN as log10,aO as _longestText,aP as _filterBetween,aQ as _lookup}from"./chunks/helpers.dataset.js";import"@kurkle/color";class Animator{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){const a=e.listeners[s],n=e.duration;a.forEach((s=>s({chart:t,initial:e.initial,numSteps:n,currentStep:Math.min(i-e.start,n)})))}_refresh(){this._request||(this._running=!0,this._request=requestAnimFrame.call(window,(()=>{this._update(),this._request=null,this._running&&this._refresh()})))}_update(t=Date.now()){let e=0;this._charts.forEach(((i,s)=>{if(!i.running||!i.items.length)return;const a=i.items;let n,o=a.length-1,r=!1;for(;o>=0;--o)n=a[o],n._active?(n._total>i.duration&&(i.duration=n._total),n.tick(t),r=!0):(a[o]=a[a.length-1],a.pop());r&&(s.draw(),this._notify(s,i,t,"progress")),a.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=a.length})),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce(((t,e)=>Math.max(t,e._duration)),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var animator=new Animator;const transparent="transparent",interpolators={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){const s=color(t||transparent),a=s.valid&&color(e||transparent);return a&&a.valid?a.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class Animation{constructor(t,e,i,s){const a=e[i];s=resolve([t.to,s,a,t.from]);const n=resolve([t.from,a,s]);this._active=!0,this._fn=t.fn||interpolators[t.type||typeof n],this._easing=effects[t.easing]||effects.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=n,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const s=this._target[this._prop],a=i-this._start,n=this._duration-a;this._start=i,this._duration=Math.floor(Math.max(n,t.duration)),this._total+=a,this._loop=!!t.loop,this._to=resolve([t.to,e,s,t.from]),this._from=resolve([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,s=this._prop,a=this._from,n=this._loop,o=this._to;let r;if(this._active=a!==o&&(n||e<i),!this._active)return this._target[s]=o,void this._notify(!0);e<0?this._target[s]=a:(r=e/i%2,r=n&&r>1?2-r:r,r=this._easing(Math.min(1,Math.max(0,r))),this._target[s]=this._fn(a,o,r))}wait(){const t=this._promises||(this._promises=[]);return new Promise(((e,i)=>{t.push({res:e,rej:i})}))}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class Animations{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!isObject(t))return;const e=Object.keys(defaults.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach((s=>{const a=t[s];if(!isObject(a))return;const n={};for(const t of e)n[t]=a[t];(isArray(a.properties)&&a.properties||[s]).forEach((t=>{t!==s&&i.has(t)||i.set(t,n)}))}))}_animateOptions(t,e){const i=e.options,s=resolveTargetOptions(t,i);if(!s)return[];const a=this._createAnimations(s,i);return i.$shared&&awaitAll(t.options.$animations,i).then((()=>{t.options=i}),(()=>{})),a}_createAnimations(t,e){const i=this._properties,s=[],a=t.$animations||(t.$animations={}),n=Object.keys(e),o=Date.now();let r;for(r=n.length-1;r>=0;--r){const l=n[r];if("$"===l.charAt(0))continue;if("options"===l){s.push(...this._animateOptions(t,e));continue}const h=e[l];let c=a[l];const d=i.get(l);if(c){if(d&&c.active()){c.update(d,h,o);continue}c.cancel()}d&&d.duration?(a[l]=c=new Animation(d,t,l,h),s.push(c)):t[l]=h}return s}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);const i=this._createAnimations(t,e);return i.length?(animator.add(this._chart,i),!0):void 0}}function awaitAll(t,e){const i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){const a=t[s[e]];a&&a.active()&&i.push(a.wait())}return Promise.all(i)}function resolveTargetOptions(t,e){if(!e)return;let i=t.options;if(i)return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i;t.options=e}function scaleClip(t,e){const i=t&&t.options||{},s=i.reverse,a=void 0===i.min?e:0,n=void 0===i.max?e:0;return{start:s?n:a,end:s?a:n}}function defaultClip(t,e,i){if(!1===i)return!1;const s=scaleClip(t,i),a=scaleClip(e,i);return{top:a.end,right:s.end,bottom:a.start,left:s.start}}function toClip(t){let e,i,s,a;return isObject(t)?(e=t.top,i=t.right,s=t.bottom,a=t.left):e=i=s=a=t,{top:e,right:i,bottom:s,left:a,disabled:!1===t}}function getSortedDatasetIndices(t,e){const i=[],s=t._getSortedDatasetMetas(e);let a,n;for(a=0,n=s.length;a<n;++a)i.push(s[a].index);return i}function applyStack(t,e,i,s={}){const a=t.keys,n="single"===s.mode;let o,r,l,h;if(null===e)return;let c=!1;for(o=0,r=a.length;o<r;++o){if(l=+a[o],l===i){if(c=!0,s.all)continue;break}h=t.values[l],isNumberFinite(h)&&(n||0===e||sign(e)===sign(h))&&(e+=h)}return c||s.all?e:0}function convertObjectDataToArray(t,e){const{iScale:i,vScale:s}=e,a="x"===i.axis?"x":"y",n="x"===s.axis?"x":"y",o=Object.keys(t),r=new Array(o.length);let l,h,c;for(l=0,h=o.length;l<h;++l)c=o[l],r[l]={[a]:c,[n]:t[c]};return r}function isStacked(t,e){const i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function getStackKey(t,e,i){return`${t.id}.${e.id}.${i.stack||i.type}`}function getUserBounds(t){const{min:e,max:i,minDefined:s,maxDefined:a}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}function getOrCreateStack(t,e,i){const s=t[e]||(t[e]={});return s[i]||(s[i]={})}function getLastIndexInStack(t,e,i,s){for(const a of e.getMatchingVisibleMetas(s).reverse()){const e=t[a.index];if(i&&e>0||!i&&e<0)return a.index}return null}function updateStacks(t,e){const{chart:i,_cachedMeta:s}=t,a=i._stacks||(i._stacks={}),{iScale:n,vScale:o,index:r}=s,l=n.axis,h=o.axis,c=getStackKey(n,o,s),d=e.length;let u;for(let t=0;t<d;++t){const i=e[t],{[l]:n,[h]:d}=i;u=(i._stacks||(i._stacks={}))[h]=getOrCreateStack(a,c,n),u[r]=d,u._top=getLastIndexInStack(u,o,!0,s.type),u._bottom=getLastIndexInStack(u,o,!1,s.type);(u._visualValues||(u._visualValues={}))[r]=d}}function getFirstScaleId(t,e){const i=t.scales;return Object.keys(i).filter((t=>i[t].axis===e)).shift()}function createDatasetContext(t,e){return createContext(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function createDataContext(t,e,i){return createContext(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:i,index:e,mode:"default",type:"data"})}function clearStacks(t,e){const i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s){e=e||t._parsed;for(const t of e){const e=t._stacks;if(!e||void 0===e[s]||void 0===e[s][i])return;delete e[s][i],void 0!==e[s]._visualValues&&void 0!==e[s]._visualValues[i]&&delete e[s]._visualValues[i]}}}const isDirectUpdateMode=t=>"reset"===t||"none"===t,cloneIfNotShared=(t,e)=>e?t:Object.assign({},t),createStack=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:getSortedDatasetIndices(i,!0),values:null};class DatasetController{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=isStacked(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&clearStacks(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,a=e.xAxisID=valueOrDefault(i.xAxisID,getFirstScaleId(t,"x")),n=e.yAxisID=valueOrDefault(i.yAxisID,getFirstScaleId(t,"y")),o=e.rAxisID=valueOrDefault(i.rAxisID,getFirstScaleId(t,"r")),r=e.indexAxis,l=e.iAxisID=s(r,a,n,o),h=e.vAxisID=s(r,n,a,o);e.xScale=this.getScaleForId(a),e.yScale=this.getScaleForId(n),e.rScale=this.getScaleForId(o),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&unlistenArrayEvents(this._data,this),t._stacked&&clearStacks(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(isObject(e)){const t=this._cachedMeta;this._data=convertObjectDataToArray(e,t)}else if(i!==e){if(i){unlistenArrayEvents(i,this);const t=this._cachedMeta;clearStacks(t),t._parsed=[]}e&&Object.isExtensible(e)&&listenArrayEvents(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const a=e._stacked;e._stacked=isStacked(e.vScale,e),e.stack!==i.stack&&(s=!0,clearStacks(e),e.stack=i.stack),this._resyncElements(t),(s||a!==e._stacked)&&(updateStacks(this,e._parsed),e._stacked=isStacked(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:s}=this,{iScale:a,_stacked:n}=i,o=a.axis;let r,l,h,c=0===t&&e===s.length||i._sorted,d=t>0&&i._parsed[t-1];if(!1===this._parsing)i._parsed=s,i._sorted=!0,h=s;else{h=isArray(s[t])?this.parseArrayData(i,s,t,e):isObject(s[t])?this.parseObjectData(i,s,t,e):this.parsePrimitiveData(i,s,t,e);const a=()=>null===l[o]||d&&l[o]<d[o];for(r=0;r<e;++r)i._parsed[r+t]=l=h[r],c&&(a()&&(c=!1),d=l);i._sorted=c}n&&updateStacks(this,h)}parsePrimitiveData(t,e,i,s){const{iScale:a,vScale:n}=t,o=a.axis,r=n.axis,l=a.getLabels(),h=a===n,c=new Array(s);let d,u,g;for(d=0,u=s;d<u;++d)g=d+i,c[d]={[o]:h||a.parse(l[g],g),[r]:n.parse(e[g],g)};return c}parseArrayData(t,e,i,s){const{xScale:a,yScale:n}=t,o=new Array(s);let r,l,h,c;for(r=0,l=s;r<l;++r)h=r+i,c=e[h],o[r]={x:a.parse(c[0],h),y:n.parse(c[1],h)};return o}parseObjectData(t,e,i,s){const{xScale:a,yScale:n}=t,{xAxisKey:o="x",yAxisKey:r="y"}=this._parsing,l=new Array(s);let h,c,d,u;for(h=0,c=s;h<c;++h)d=h+i,u=e[d],l[h]={x:a.parse(resolveObjectKey(u,o),d),y:n.parse(resolveObjectKey(u,r),d)};return l}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const s=this.chart,a=this._cachedMeta,n=e[t.axis];return applyStack({keys:getSortedDatasetIndices(s,!0),values:e._stacks[t.axis]._visualValues},n,a.index,{mode:i})}updateRangeFromParsed(t,e,i,s){const a=i[e.axis];let n=null===a?NaN:a;const o=s&&i._stacks[e.axis];s&&o&&(s.values=o,n=applyStack(s,a,this._cachedMeta.index)),t.min=Math.min(t.min,n),t.max=Math.max(t.max,n)}getMinMax(t,e){const i=this._cachedMeta,s=i._parsed,a=i._sorted&&t===i.iScale,n=s.length,o=this._getOtherScale(t),r=createStack(e,i,this.chart),l={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:c}=getUserBounds(o);let d,u;function g(){u=s[d];const e=u[o.axis];return!isNumberFinite(u[t.axis])||h>e||c<e}for(d=0;d<n&&(g()||(this.updateRangeFromParsed(l,t,u,r),!a));++d);if(a)for(d=n-1;d>=0;--d)if(!g()){this.updateRangeFromParsed(l,t,u,r);break}return l}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let s,a,n;for(s=0,a=e.length;s<a;++s)n=e[s][t.axis],isNumberFinite(n)&&i.push(n);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,s=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:s?""+s.getLabelForValue(a[s.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=toClip(valueOrDefault(this.options.clip,defaultClip(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],a=e.chartArea,n=[],o=this._drawStart||0,r=this._drawCount||s.length-o,l=this.options.drawActiveElementsOnTop;let h;for(i.dataset&&i.dataset.draw(t,a,o,r),h=o;h<o+r;++h){const e=s[h];e.hidden||(e.active&&l?n.push(e):e.draw(t,a))}for(h=0;h<n.length;++h)n[h].draw(t,a)}getStyle(t,e){const i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const s=this.getDataset();let a;if(t>=0&&t<this._cachedMeta.data.length){const e=this._cachedMeta.data[t];a=e.$context||(e.$context=createDataContext(this.getContext(),t,e)),a.parsed=this.getParsed(t),a.raw=s.data[t],a.index=a.dataIndex=t}else a=this.$context||(this.$context=createDatasetContext(this.chart.getContext(),this.index)),a.dataset=s,a.index=a.datasetIndex=this.index;return a.active=!!e,a.mode=i,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const s="active"===e,a=this._cachedDataOpts,n=t+"-"+e,o=a[n],r=this.enableOptionSharing&&defined(i);if(o)return cloneIfNotShared(o,r);const l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),c=s?[`${t}Hover`,"hover",t,""]:[t,""],d=l.getOptionScopes(this.getDataset(),h),u=Object.keys(defaults.elements[t]),g=l.resolveNamedOptions(d,u,(()=>this.getContext(i,s,e)),c);return g.$shared&&(g.$shared=r,a[n]=Object.freeze(cloneIfNotShared(g,r))),g}_resolveAnimations(t,e,i){const s=this.chart,a=this._cachedDataOpts,n=`animation-${e}`,o=a[n];if(o)return o;let r;if(!1!==s.options.animation){const s=this.chart.config,a=s.datasetAnimationScopeKeys(this._type,e),n=s.getOptionScopes(this.getDataset(),a);r=s.createResolver(n,this.getContext(t,i,e))}const l=new Animations(s,r&&r.animations);return r&&r._cacheable&&(a[n]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||isDirectUpdateMode(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,a=this.getSharedOptions(i),n=this.includeOptions(e,a)||a!==s;return this.updateSharedOptions(a,e,i),{sharedOptions:a,includeOptions:n}}updateElement(t,e,i,s){isDirectUpdateMode(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!isDirectUpdateMode(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;const a=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(a)||a})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];const s=i.length,a=e.length,n=Math.min(a,s);n&&this.parse(0,n),a>s?this._insertElements(s,a-s,t):a<s&&this._removeElements(a,s-a)}_insertElements(t,e,i=!0){const s=this._cachedMeta,a=s.data,n=t+e;let o;const r=t=>{for(t.length+=e,o=t.length-1;o>=n;o--)t[o]=t[o-e]};for(r(a),o=t;o<n;++o)a[o]=new this.dataElementType;this._parsing&&r(s._parsed),this.parse(t,e),i&&this.updateElements(a,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,e);i._stacked&&clearStacks(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function getAllScaleValues(t,e){if(!t._cache.$bar){const i=t.getMatchingVisibleMetas(e);let s=[];for(let e=0,a=i.length;e<a;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=_arrayUnique(s.sort(((t,e)=>t-e)))}return t._cache.$bar}function computeMinSampleSize(t){const e=t.iScale,i=getAllScaleValues(e,t.type);let s,a,n,o,r=e._length;const l=()=>{32767!==n&&-32768!==n&&(defined(o)&&(r=Math.min(r,Math.abs(n-o)||r)),o=n)};for(s=0,a=i.length;s<a;++s)n=e.getPixelForValue(i[s]),l();for(o=void 0,s=0,a=e.ticks.length;s<a;++s)n=e.getPixelForTick(s),l();return r}function computeFitCategoryTraits(t,e,i,s){const a=i.barThickness;let n,o;return isNullOrUndef(a)?(n=e.min*i.categoryPercentage,o=i.barPercentage):(n=a*s,o=1),{chunk:n/s,ratio:o,start:e.pixels[t]-n/2}}function computeFlexCategoryTraits(t,e,i,s){const a=e.pixels,n=a[t];let o=t>0?a[t-1]:null,r=t<a.length-1?a[t+1]:null;const l=i.categoryPercentage;null===o&&(o=n-(null===r?e.end-e.start:r-n)),null===r&&(r=n+n-o);const h=n-(n-Math.min(o,r))/2*l;return{chunk:Math.abs(r-o)/2*l/s,ratio:i.barPercentage,start:h}}function parseFloatBar(t,e,i,s){const a=i.parse(t[0],s),n=i.parse(t[1],s),o=Math.min(a,n),r=Math.max(a,n);let l=o,h=r;Math.abs(o)>Math.abs(r)&&(l=r,h=o),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:a,end:n,min:o,max:r}}function parseValue(t,e,i,s){return isArray(t)?parseFloatBar(t,e,i,s):e[i.axis]=i.parse(t,s),e}function parseArrayOrPrimitive(t,e,i,s){const a=t.iScale,n=t.vScale,o=a.getLabels(),r=a===n,l=[];let h,c,d,u;for(h=i,c=i+s;h<c;++h)u=e[h],d={},d[a.axis]=r||a.parse(o[h],h),l.push(parseValue(u,d,n,h));return l}function isFloatBar(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function barSign(t,e,i){return 0!==t?sign(t):(e.isHorizontal()?1:-1)*(e.min>=i?1:-1)}function borderProps(t){let e,i,s,a,n;return t.horizontal?(e=t.base>t.x,i="left",s="right"):(e=t.base<t.y,i="bottom",s="top"),e?(a="end",n="start"):(a="start",n="end"),{start:i,end:s,reverse:e,top:a,bottom:n}}function setBorderSkipped(t,e,i,s){let a=e.borderSkipped;const n={};if(!a)return void(t.borderSkipped=n);if(!0===a)return void(t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:o,end:r,reverse:l,top:h,bottom:c}=borderProps(t);"middle"===a&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?a=h:(i._bottom||0)===s?a=c:(n[parseEdge(c,o,r,l)]=!0,a=h)),n[parseEdge(a,o,r,l)]=!0,t.borderSkipped=n}function parseEdge(t,e,i,s){return t=s?startEnd(t=swap(t,e,i),i,e):startEnd(t,e,i)}function swap(t,e,i){return t===e?i:t===i?e:t}function startEnd(t,e,i){return"start"===t?e:"end"===t?i:t}function setInflateAmount(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?1===i?.33:0:e}class BarController extends DatasetController{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return parseArrayOrPrimitive(t,e,i,s)}parseArrayData(t,e,i,s){return parseArrayOrPrimitive(t,e,i,s)}parseObjectData(t,e,i,s){const{iScale:a,vScale:n}=t,{xAxisKey:o="x",yAxisKey:r="y"}=this._parsing,l="x"===a.axis?o:r,h="x"===n.axis?o:r,c=[];let d,u,g,p;for(d=i,u=i+s;d<u;++d)p=e[d],g={},g[a.axis]=a.parse(resolveObjectKey(p,l),d),c.push(parseValue(resolveObjectKey(p,h),g,n,d));return c}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);const a=i._custom;a&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:i,vScale:s}=e,a=this.getParsed(t),n=a._custom,o=isFloatBar(n)?"["+n.start+", "+n.end+"]":""+s.getLabelForValue(a[s.axis]);return{label:""+i.getLabelForValue(a[i.axis]),value:o}}initialize(){this.enableOptionSharing=!0,super.initialize();this._cachedMeta.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){const a="reset"===s,{index:n,_cachedMeta:{vScale:o}}=this,r=o.getBasePixel(),l=o.isHorizontal(),h=this._getRuler(),{sharedOptions:c,includeOptions:d}=this._getSharedOptions(e,s);for(let u=e;u<e+i;u++){const e=this.getParsed(u),i=a||isNullOrUndef(e[o.axis])?{base:r,head:r}:this._calculateBarValuePixels(u),g=this._calculateBarIndexPixels(u,h),p=(e._stacks||{})[o.axis],f={horizontal:l,base:i.base,enableBorderRadius:!p||isFloatBar(e._custom)||n===p._top||n===p._bottom,x:l?i.head:g.center,y:l?g.center:i.head,height:l?g.size:Math.abs(i.size),width:l?Math.abs(i.size):g.size};d&&(f.options=c||this.resolveDataElementOptions(u,t[u].active?"active":s));const m=f.options||t[u].options;setBorderSkipped(f,m,p,n),setInflateAmount(f,m,h.ratio),this.updateElement(t[u],u,f,s)}}_getStacks(t,e){const{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter((t=>t.controller.options.grouped)),a=i.options.stacked,n=[],o=this._cachedMeta.controller.getParsed(e),r=o&&o[i.axis],l=t=>{const e=t._parsed.find((t=>t[i.axis]===r)),s=e&&e[t.vScale.axis];if(isNullOrUndef(s)||isNaN(s))return!0};for(const i of s)if((void 0===e||!l(i))&&((!1===a||-1===n.indexOf(i.stack)||void 0===a&&void 0===i.stack)&&n.push(i.stack),i.index===t))break;return n.length||n.push(void 0),n}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter((i=>t[i].axis===e)).shift()}_getAxis(){const t={},e=this.getFirstScaleIdForIndexAxis();for(const i of this.chart.data.datasets)t[valueOrDefault("x"===this.chart.options.indexAxis?i.xAxisID:i.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,i){const s=this._getStacks(t,i),a=void 0!==e?s.indexOf(e):-1;return-1===a?s.length-1:a}_getRuler(){const t=this.options,e=this._cachedMeta,i=e.iScale,s=[];let a,n;for(a=0,n=e.data.length;a<n;++a)s.push(i.getPixelForValue(this.getParsed(a)[i.axis],a));const o=t.barThickness;return{min:o||computeMinSampleSize(e),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:o?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:i,index:s},options:{base:a,minBarLength:n}}=this,o=a||0,r=this.getParsed(t),l=r._custom,h=isFloatBar(l);let c,d,u=r[e.axis],g=0,p=i?this.applyStack(e,r,i):u;p!==u&&(g=p-u,p=u),h&&(u=l.barStart,p=l.barEnd-l.barStart,0!==u&&sign(u)!==sign(l.barEnd)&&(g=0),g+=u);const f=isNullOrUndef(a)||h?g:a;let m=e.getPixelForValue(f);if(c=this.chart.getDataVisibility(t)?e.getPixelForValue(g+p):m,d=c-m,Math.abs(d)<n){d=barSign(d,e,o)*n,u===o&&(m-=d/2);const t=e.getPixelForDecimal(0),a=e.getPixelForDecimal(1),l=Math.min(t,a),g=Math.max(t,a);m=Math.max(Math.min(m,g),l),c=m+d,i&&!h&&(r._stacks[e.axis]._visualValues[s]=e.getValueForPixel(c)-e.getValueForPixel(m))}if(m===e.getPixelForValue(o)){const t=sign(d)*e.getLineWidthForValue(o)/2;m+=t,d-=t}return{size:d,base:m,head:c,center:c+d/2}}_calculateBarIndexPixels(t,e){const i=e.scale,s=this.options,a=s.skipNull,n=valueOrDefault(s.maxBarThickness,1/0);let o,r;const l=this._getAxisCount();if(e.grouped){const i=a?this._getStackCount(t):e.stackCount,h="flex"===s.barThickness?computeFlexCategoryTraits(t,e,s,i*l):computeFitCategoryTraits(t,e,s,i*l),c="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,d=this._getAxis().indexOf(valueOrDefault(c,this.getFirstScaleIdForIndexAxis())),u=this._getStackIndex(this.index,this._cachedMeta.stack,a?t:void 0)+d;o=h.start+h.chunk*u+h.chunk/2,r=Math.min(n,h.chunk*h.ratio)}else o=i.getPixelForValue(this.getParsed(t)[i.axis],t),r=Math.min(n,e.min*e.ratio);return{base:o-r/2,head:o+r/2,center:o,size:r}}draw(){const t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length;let a=0;for(;a<s;++a)null===this.getParsed(a)[e.axis]||i[a].hidden||i[a].draw(this._ctx)}}class BubbleController extends DatasetController{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){const a=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<a.length;t++)a[t]._custom=this.resolveDataElementOptions(t+i).radius;return a}parseArrayData(t,e,i,s){const a=super.parseArrayData(t,e,i,s);for(let t=0;t<a.length;t++){const s=e[i+t];a[t]._custom=valueOrDefault(s[2],this.resolveDataElementOptions(t+i).radius)}return a}parseObjectData(t,e,i,s){const a=super.parseObjectData(t,e,i,s);for(let t=0;t<a.length;t++){const s=e[i+t];a[t]._custom=valueOrDefault(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return a}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),o=s.getLabelForValue(n.x),r=a.getLabelForValue(n.y),l=n._custom;return{label:i[t]||"",value:"("+o+", "+r+(l?", "+l:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){const a="reset"===s,{iScale:n,vScale:o}=this._cachedMeta,{sharedOptions:r,includeOptions:l}=this._getSharedOptions(e,s),h=n.axis,c=o.axis;for(let d=e;d<e+i;d++){const e=t[d],i=!a&&this.getParsed(d),u={},g=u[h]=a?n.getPixelForDecimal(.5):n.getPixelForValue(i[h]),p=u[c]=a?o.getBasePixel():o.getPixelForValue(i[c]);u.skip=isNaN(g)||isNaN(p),l&&(u.options=r||this.resolveDataElementOptions(d,e.active?"active":s),a&&(u.options.radius=0)),this.updateElement(e,d,u,s)}}resolveDataElementOptions(t,e){const i=this.getParsed(t);let s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));const a=s.radius;return"active"!==e&&(s.radius=0),s.radius+=valueOrDefault(i&&i._custom,a),s}}function getRatioAndOffset(t,e,i){let s=1,a=1,n=0,o=0;if(e<TAU){const r=t,l=r+e,h=Math.cos(r),c=Math.sin(r),d=Math.cos(l),u=Math.sin(l),g=(t,e,s)=>_angleBetween(t,r,l,!0)?1:Math.max(e,e*i,s,s*i),p=(t,e,s)=>_angleBetween(t,r,l,!0)?-1:Math.min(e,e*i,s,s*i),f=g(0,h,d),m=g(HALF_PI,c,u),x=p(PI,h,d),b=p(PI+HALF_PI,c,u);s=(f-x)/2,a=(m-b)/2,n=-(f+x)/2,o=-(m+b)/2}return{ratioX:s,ratioY:a,offsetX:n,offsetY:o}}class DoughnutController extends DatasetController{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map(((e,a)=>{const n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}}))}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let a,n,o=t=>+i[t];if(isObject(i[t])){const{key:t="value"}=this._parsing;o=e=>+resolveObjectKey(i[e],t)}for(a=t,n=t+e;a<n;++a)s._parsed[a]=o(a)}}_getRotation(){return toRadians(this.options.rotation-90)}_getCircumference(){return toRadians(this.options.circumference)}_getRotationExtents(){let t=TAU,e=-TAU;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,a=s._getRotation(),n=s._getCircumference();t=Math.min(t,a),e=Math.max(e,a+n)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:i}=e,s=this._cachedMeta,a=s.data,n=this.getMaxBorderWidth()+this.getMaxOffset(a)+this.options.spacing,o=Math.max((Math.min(i.width,i.height)-n)/2,0),r=Math.min(toPercentage(this.options.cutout,o),1),l=this._getRingWeight(this.index),{circumference:h,rotation:c}=this._getRotationExtents(),{ratioX:d,ratioY:u,offsetX:g,offsetY:p}=getRatioAndOffset(c,h,r),f=(i.width-n)/d,m=(i.height-n)/u,x=Math.max(Math.min(f,m)/2,0),b=toDimension(this.options.radius,x),_=(b-Math.max(b*r,0))/this._getVisibleDatasetWeightTotal();this.offsetX=g*b,this.offsetY=p*b,s.total=this.calculateTotal(),this.outerRadius=b-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*l,0),this.updateElements(a,0,a.length,t)}_circumference(t,e){const i=this.options,s=this._cachedMeta,a=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*a/TAU)}updateElements(t,e,i,s){const a="reset"===s,n=this.chart,o=n.chartArea,r=n.options.animation,l=(o.left+o.right)/2,h=(o.top+o.bottom)/2,c=a&&r.animateScale,d=c?0:this.innerRadius,u=c?0:this.outerRadius,{sharedOptions:g,includeOptions:p}=this._getSharedOptions(e,s);let f,m=this._getRotation();for(f=0;f<e;++f)m+=this._circumference(f,a);for(f=e;f<e+i;++f){const e=this._circumference(f,a),i=t[f],n={x:l+this.offsetX,y:h+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:u,innerRadius:d};p&&(n.options=g||this.resolveDataElementOptions(f,i.active?"active":s)),m+=e,this.updateElement(i,f,n,s)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let i,s=0;for(i=0;i<e.length;i++){const a=t._parsed[i];null===a||isNaN(a)||!this.chart.getDataVisibility(i)||e[i].hidden||(s+=Math.abs(a))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?TAU*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=formatNumber(e._parsed[t],i.options.locale);return{label:s[t]||"",value:a}}getMaxBorderWidth(t){let e=0;const i=this.chart;let s,a,n,o,r;if(!t)for(s=0,a=i.data.datasets.length;s<a;++s)if(i.isDatasetVisible(s)){n=i.getDatasetMeta(s),t=n.data,o=n.controller;break}if(!t)return 0;for(s=0,a=t.length;s<a;++s)r=o.resolveDataElementOptions(s),"inner"!==r.borderAlign&&(e=Math.max(e,r.borderWidth||0,r.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){const t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(valueOrDefault(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class LineController extends DatasetController{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:s=[],_dataset:a}=e,n=this.chart._animationsDisabled;let{start:o,count:r}=_getStartAndCountOfVisiblePoints(e,s,n);this._drawStart=o,this._drawCount=r,_scaleRangesChanged(e)&&(o=0,r=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!a._decimated,i.points=s;const l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!n,options:l},t),this.updateElements(s,o,r,t)}updateElements(t,e,i,s){const a="reset"===s,{iScale:n,vScale:o,_stacked:r,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:c}=this._getSharedOptions(e,s),d=n.axis,u=o.axis,{spanGaps:g,segment:p}=this.options,f=isNumber(g)?g:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||a||"none"===s,x=e+i,b=t.length;let _=e>0&&this.getParsed(e-1);for(let i=0;i<b;++i){const g=t[i],b=m?g:{};if(i<e||i>=x){b.skip=!0;continue}const y=this.getParsed(i),v=isNullOrUndef(y[u]),k=b[d]=n.getPixelForValue(y[d],i),S=b[u]=a||v?o.getBasePixel():o.getPixelForValue(r?this.applyStack(o,y,r):y[u],i);b.skip=isNaN(k)||isNaN(S)||v,b.stop=i>0&&Math.abs(y[d]-_[d])>f,p&&(b.parsed=y,b.raw=l.data[i]),c&&(b.options=h||this.resolveDataElementOptions(i,g.active?"active":s)),m||this.updateElement(g,i,b,s),_=y}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;const a=s[0].size(this.resolveDataElementOptions(0)),n=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,a,n)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class PolarAreaController extends DatasetController{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map(((e,a)=>{const n=t.getDatasetMeta(0).controller.getStyle(a);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(a),index:a}}))}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,s=i.data.labels||[],a=formatNumber(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:a}}parseObjectData(t,e,i,s){return _parseObjectDataRadialScale.bind(this)(t,e,i,s)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach(((t,i)=>{const s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))})),e}_updateRadius(){const t=this.chart,e=t.chartArea,i=t.options,s=Math.min(e.right-e.left,e.bottom-e.top),a=Math.max(s/2,0),n=(a-Math.max(i.cutoutPercentage?a/100*i.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=a-n*this.index,this.innerRadius=this.outerRadius-n}updateElements(t,e,i,s){const a="reset"===s,n=this.chart,o=n.options.animation,r=this._cachedMeta.rScale,l=r.xCenter,h=r.yCenter,c=r.getIndexAngle(0)-.5*PI;let d,u=c;const g=360/this.countVisibleElements();for(d=0;d<e;++d)u+=this._computeAngle(d,s,g);for(d=e;d<e+i;d++){const e=t[d];let i=u,p=u+this._computeAngle(d,s,g),f=n.getDataVisibility(d)?r.getDistanceFromCenterForValue(this.getParsed(d).r):0;u=p,a&&(o.animateScale&&(f=0),o.animateRotate&&(i=p=c));const m={x:l,y:h,innerRadius:0,outerRadius:f,startAngle:i,endAngle:p,options:this.resolveDataElementOptions(d,e.active?"active":s)};this.updateElement(e,d,m,s)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach(((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++})),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?toRadians(this.resolveDataElementOptions(t,e).angle||i):0}}class PieController extends DoughnutController{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class RadarController extends DatasetController{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return _parseObjectDataRadialScale.bind(this)(t,e,i,s)}update(t){const e=this._cachedMeta,i=e.dataset,s=e.data||[],a=e.iScale.getLabels();if(i.points=s,"resize"!==t){const e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);const n={_loop:!0,_fullLoop:a.length===s.length,options:e};this.updateElement(i,void 0,n,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){const a=this._cachedMeta.rScale,n="reset"===s;for(let o=e;o<e+i;o++){const e=t[o],i=this.resolveDataElementOptions(o,e.active?"active":s),r=a.getPointPositionForValue(o,this.getParsed(o).r),l=n?a.xCenter:r.x,h=n?a.yCenter:r.y,c={x:l,y:h,angle:r.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,o,c,s)}}}class ScatterController extends DatasetController{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:a}=e,n=this.getParsed(t),o=s.getLabelForValue(n.x),r=a.getLabelForValue(n.y);return{label:i[t]||"",value:"("+o+", "+r+")"}}update(t){const e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled;let{start:a,count:n}=_getStartAndCountOfVisiblePoints(e,i,s);if(this._drawStart=a,this._drawCount=n,_scaleRangesChanged(e)&&(a=0,n=i.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:n}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!n._decimated,a.points=i;const o=this.resolveDatasetElementOptions(t);o.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:o},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,a,n,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){const a="reset"===s,{iScale:n,vScale:o,_stacked:r,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),c=this.getSharedOptions(h),d=this.includeOptions(s,c),u=n.axis,g=o.axis,{spanGaps:p,segment:f}=this.options,m=isNumber(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||a||"none"===s;let b=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){const e=t[h],i=this.getParsed(h),p=x?e:{},_=isNullOrUndef(i[g]),y=p[u]=n.getPixelForValue(i[u],h),v=p[g]=a||_?o.getBasePixel():o.getPixelForValue(r?this.applyStack(o,i,r):i[g],h);p.skip=isNaN(y)||isNaN(v)||_,p.stop=h>0&&Math.abs(i[u]-b[u])>m,f&&(p.parsed=i,p.raw=l.data[h]),d&&(p.options=c||this.resolveDataElementOptions(h,e.active?"active":s)),x||this.updateElement(e,h,p,s),b=i}this.updateSharedOptions(c,s,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}const i=t.dataset,s=i.options&&i.options.borderWidth||0;if(!e.length)return s;const a=e[0].size(this.resolveDataElementOptions(0)),n=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(s,a,n)/2}}var controllers=Object.freeze({__proto__:null,BarController:BarController,BubbleController:BubbleController,DoughnutController:DoughnutController,LineController:LineController,PieController:PieController,PolarAreaController:PolarAreaController,RadarController:RadarController,ScatterController:ScatterController});function abstract(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class DateAdapterBase{static override(t){Object.assign(DateAdapterBase.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return abstract()}parse(){return abstract()}format(){return abstract()}add(){return abstract()}diff(){return abstract()}startOf(){return abstract()}endOf(){return abstract()}}var adapters={_date:DateAdapterBase};function binarySearch(t,e,i,s){const{controller:a,data:n,_sorted:o}=t,r=a._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(r&&e===r.axis&&"r"!==e&&o&&n.length){const o=r._reversePixels?_rlookupByKey:_lookupByKey;if(!s){const s=o(n,e,i);if(l){const{vScale:e}=a._cachedMeta,{_parsed:i}=t,n=i.slice(0,s.lo+1).reverse().findIndex((t=>!isNullOrUndef(t[e.axis])));s.lo-=Math.max(0,n);const o=i.slice(s.hi).findIndex((t=>!isNullOrUndef(t[e.axis])));s.hi+=Math.max(0,o)}return s}if(a._sharedOptions){const t=n[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){const t=o(n,e,i-s),a=o(n,e,i+s);return{lo:t.lo,hi:a.hi}}}}return{lo:0,hi:n.length-1}}function evaluateInteractionItems(t,e,i,s,a){const n=t.getSortedVisibleDatasetMetas(),o=i[e];for(let t=0,i=n.length;t<i;++t){const{index:i,data:r}=n[t],{lo:l,hi:h}=binarySearch(n[t],e,o,a);for(let t=l;t<=h;++t){const e=r[t];e.skip||s(e,i,t)}}}function getDistanceMetricForAxis(t){const e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){const a=e?Math.abs(t.x-s.x):0,n=i?Math.abs(t.y-s.y):0;return Math.sqrt(Math.pow(a,2)+Math.pow(n,2))}}function getIntersectItems(t,e,i,s,a){const n=[];if(!a&&!t.isPointInArea(e))return n;return evaluateInteractionItems(t,i,e,(function(i,o,r){(a||_isPointInArea(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&n.push({element:i,datasetIndex:o,index:r})}),!0),n}function getNearestRadialItems(t,e,i,s){let a=[];return evaluateInteractionItems(t,i,e,(function(t,i,n){const{startAngle:o,endAngle:r}=t.getProps(["startAngle","endAngle"],s),{angle:l}=getAngleFromPoint(t,{x:e.x,y:e.y});_angleBetween(l,o,r)&&a.push({element:t,datasetIndex:i,index:n})})),a}function getNearestCartesianItems(t,e,i,s,a,n){let o=[];const r=getDistanceMetricForAxis(i);let l=Number.POSITIVE_INFINITY;return evaluateInteractionItems(t,i,e,(function(i,h,c){const d=i.inRange(e.x,e.y,a);if(s&&!d)return;const u=i.getCenterPoint(a);if(!(!!n||t.isPointInArea(u))&&!d)return;const g=r(e,u);g<l?(o=[{element:i,datasetIndex:h,index:c}],l=g):g===l&&o.push({element:i,datasetIndex:h,index:c})})),o}function getNearestItems(t,e,i,s,a,n){return n||t.isPointInArea(e)?"r"!==i||s?getNearestCartesianItems(t,e,i,s,a,n):getNearestRadialItems(t,e,i,a):[]}function getAxisItems(t,e,i,s,a){const n=[],o="x"===i?"inXRange":"inYRange";let r=!1;return evaluateInteractionItems(t,i,e,((t,s,l)=>{t[o]&&t[o](e[i],a)&&(n.push({element:t,datasetIndex:s,index:l}),r=r||t.inRange(e.x,e.y,a))})),s&&!r?[]:n}var Interaction={evaluateInteractionItems:evaluateInteractionItems,modes:{index(t,e,i,s){const a=getRelativePosition(e,t),n=i.axis||"x",o=i.includeInvisible||!1,r=i.intersect?getIntersectItems(t,a,n,s,o):getNearestItems(t,a,n,!1,s,o),l=[];return r.length?(t.getSortedVisibleDatasetMetas().forEach((t=>{const e=r[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})})),l):[]},dataset(t,e,i,s){const a=getRelativePosition(e,t),n=i.axis||"xy",o=i.includeInvisible||!1;let r=i.intersect?getIntersectItems(t,a,n,s,o):getNearestItems(t,a,n,!1,s,o);if(r.length>0){const e=r[0].datasetIndex,i=t.getDatasetMeta(e).data;r=[];for(let t=0;t<i.length;++t)r.push({element:i[t],datasetIndex:e,index:t})}return r},point:(t,e,i,s)=>getIntersectItems(t,getRelativePosition(e,t),i.axis||"xy",s,i.includeInvisible||!1),nearest(t,e,i,s){const a=getRelativePosition(e,t),n=i.axis||"xy",o=i.includeInvisible||!1;return getNearestItems(t,a,n,i.intersect,s,o)},x:(t,e,i,s)=>getAxisItems(t,getRelativePosition(e,t),"x",i.intersect,s),y:(t,e,i,s)=>getAxisItems(t,getRelativePosition(e,t),"y",i.intersect,s)}};const STATIC_POSITIONS=["left","top","right","bottom"];function filterByPosition(t,e){return t.filter((t=>t.pos===e))}function filterDynamicPositionByAxis(t,e){return t.filter((t=>-1===STATIC_POSITIONS.indexOf(t.pos)&&t.box.axis===e))}function sortByWeight(t,e){return t.sort(((t,i)=>{const s=e?i:t,a=e?t:i;return s.weight===a.weight?s.index-a.index:s.weight-a.weight}))}function wrapBoxes(t){const e=[];let i,s,a,n,o,r;for(i=0,s=(t||[]).length;i<s;++i)a=t[i],({position:n,options:{stack:o,stackWeight:r=1}}=a),e.push({index:i,box:a,pos:n,horizontal:a.isHorizontal(),weight:a.weight,stack:o&&n+o,stackWeight:r});return e}function buildStacks(t){const e={};for(const i of t){const{stack:t,pos:s,stackWeight:a}=i;if(!t||!STATIC_POSITIONS.includes(s))continue;const n=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});n.count++,n.weight+=a}return e}function setLayoutDims(t,e){const i=buildStacks(t),{vBoxMaxWidth:s,hBoxMaxHeight:a}=e;let n,o,r;for(n=0,o=t.length;n<o;++n){r=t[n];const{fullSize:o}=r.box,l=i[r.stack],h=l&&r.stackWeight/l.weight;r.horizontal?(r.width=h?h*s:o&&e.availableWidth,r.height=a):(r.width=s,r.height=h?h*a:o&&e.availableHeight)}return i}function buildLayoutBoxes(t){const e=wrapBoxes(t),i=sortByWeight(e.filter((t=>t.box.fullSize)),!0),s=sortByWeight(filterByPosition(e,"left"),!0),a=sortByWeight(filterByPosition(e,"right")),n=sortByWeight(filterByPosition(e,"top"),!0),o=sortByWeight(filterByPosition(e,"bottom")),r=filterDynamicPositionByAxis(e,"x"),l=filterDynamicPositionByAxis(e,"y");return{fullSize:i,leftAndTop:s.concat(n),rightAndBottom:a.concat(l).concat(o).concat(r),chartArea:filterByPosition(e,"chartArea"),vertical:s.concat(a).concat(l),horizontal:n.concat(o).concat(r)}}function getCombinedMax(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function updateMaxPadding(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function updateDims(t,e,i,s){const{pos:a,box:n}=i,o=t.maxPadding;if(!isObject(a)){i.size&&(t[a]-=i.size);const e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?n.height:n.width),i.size=e.size/e.count,t[a]+=i.size}n.getPadding&&updateMaxPadding(o,n.getPadding());const r=Math.max(0,e.outerWidth-getCombinedMax(o,t,"left","right")),l=Math.max(0,e.outerHeight-getCombinedMax(o,t,"top","bottom")),h=r!==t.w,c=l!==t.h;return t.w=r,t.h=l,i.horizontal?{same:h,other:c}:{same:c,other:h}}function handleMaxPadding(t){const e=t.maxPadding;function i(i){const s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}function getMargins(t,e){const i=e.maxPadding;function s(t){const s={left:0,top:0,right:0,bottom:0};return t.forEach((t=>{s[t]=Math.max(e[t],i[t])})),s}return s(t?["left","right"]:["top","bottom"])}function fitBoxes(t,e,i,s){const a=[];let n,o,r,l,h,c;for(n=0,o=t.length,h=0;n<o;++n){r=t[n],l=r.box,l.update(r.width||e.w,r.height||e.h,getMargins(r.horizontal,e));const{same:o,other:d}=updateDims(e,i,r,s);h|=o&&a.length,c=c||d,l.fullSize||a.push(r)}return h&&fitBoxes(a,e,i,s)||c}function setBoxDims(t,e,i,s,a){t.top=i,t.left=e,t.right=e+s,t.bottom=i+a,t.width=s,t.height=a}function placeBoxes(t,e,i,s){const a=i.padding;let{x:n,y:o}=e;for(const r of t){const t=r.box,l=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/l.weight||1;if(r.horizontal){const s=e.w*h,n=l.size||t.height;defined(l.start)&&(o=l.start),t.fullSize?setBoxDims(t,a.left,o,i.outerWidth-a.right-a.left,n):setBoxDims(t,e.left+l.placed,o,s,n),l.start=o,l.placed+=s,o=t.bottom}else{const s=e.h*h,o=l.size||t.width;defined(l.start)&&(n=l.start),t.fullSize?setBoxDims(t,n,a.top,o,i.outerHeight-a.bottom-a.top):setBoxDims(t,n,e.top+l.placed,o,s),l.start=n,l.placed+=s,n=t.right}}e.x=n,e.y=o}var layouts={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){const i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;const a=toPadding(t.options.layout.padding),n=Math.max(e-a.width,0),o=Math.max(i-a.height,0),r=buildLayoutBoxes(t.boxes),l=r.vertical,h=r.horizontal;each(t.boxes,(t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()}));const c=l.reduce(((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1),0)||1,d=Object.freeze({outerWidth:e,outerHeight:i,padding:a,availableWidth:n,availableHeight:o,vBoxMaxWidth:n/2/c,hBoxMaxHeight:o/2}),u=Object.assign({},a);updateMaxPadding(u,toPadding(s));const g=Object.assign({maxPadding:u,w:n,h:o,x:a.left,y:a.top},a),p=setLayoutDims(l.concat(h),d);fitBoxes(r.fullSize,g,d,p),fitBoxes(l,g,d,p),fitBoxes(h,g,d,p)&&fitBoxes(l,g,d,p),handleMaxPadding(g),placeBoxes(r.leftAndTop,g,d,p),g.x+=g.w,g.y+=g.h,placeBoxes(r.rightAndBottom,g,d,p),t.chartArea={left:g.left,top:g.top,right:g.left+g.w,bottom:g.top+g.h,height:g.h,width:g.w},each(r.chartArea,(e=>{const i=e.box;Object.assign(i,t.chartArea),i.update(g.w,g.h,{left:0,top:0,right:0,bottom:0})}))}};class BasePlatform{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class BasicPlatform extends BasePlatform{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const EXPANDO_KEY="$chartjs",EVENT_TYPES={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},isNullOrEmpty=t=>null===t||""===t;function initCanvas(t,e){const i=t.style,s=t.getAttribute("height"),a=t.getAttribute("width");if(t.$chartjs={initial:{height:s,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",isNullOrEmpty(a)){const e=readUsedSize(t,"width");void 0!==e&&(t.width=e)}if(isNullOrEmpty(s))if(""===t.style.height)t.height=t.width/(e||2);else{const e=readUsedSize(t,"height");void 0!==e&&(t.height=e)}return t}const eventListenerOptions=!!supportsEventListenerOptions&&{passive:!0};function addListener(t,e,i){t&&t.addEventListener(e,i,eventListenerOptions)}function removeListener(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,eventListenerOptions)}function fromNativeEvent(t,e){const i=EVENT_TYPES[t.type]||t.type,{x:s,y:a}=getRelativePosition(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==a?a:null}}function nodeListContains(t,e){for(const i of t)if(i===e||i.contains(e))return!0}function createAttachObserver(t,e,i){const s=t.canvas,a=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||nodeListContains(i.addedNodes,s),e=e&&!nodeListContains(i.removedNodes,s);e&&i()}));return a.observe(document,{childList:!0,subtree:!0}),a}function createDetachObserver(t,e,i){const s=t.canvas,a=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||nodeListContains(i.removedNodes,s),e=e&&!nodeListContains(i.addedNodes,s);e&&i()}));return a.observe(document,{childList:!0,subtree:!0}),a}const drpListeningCharts=new Map;let oldDevicePixelRatio=0;function onWindowResize(){const t=window.devicePixelRatio;t!==oldDevicePixelRatio&&(oldDevicePixelRatio=t,drpListeningCharts.forEach(((e,i)=>{i.currentDevicePixelRatio!==t&&e()})))}function listenDevicePixelRatioChanges(t,e){drpListeningCharts.size||window.addEventListener("resize",onWindowResize),drpListeningCharts.set(t,e)}function unlistenDevicePixelRatioChanges(t){drpListeningCharts.delete(t),drpListeningCharts.size||window.removeEventListener("resize",onWindowResize)}function createResizeObserver(t,e,i){const s=t.canvas,a=s&&_getParentNode(s);if(!a)return;const n=throttled(((t,e)=>{const s=a.clientWidth;i(t,e),s<a.clientWidth&&i()}),window),o=new ResizeObserver((t=>{const e=t[0],i=e.contentRect.width,s=e.contentRect.height;0===i&&0===s||n(i,s)}));return o.observe(a),listenDevicePixelRatioChanges(t,n),o}function releaseObserver(t,e,i){i&&i.disconnect(),"resize"===e&&unlistenDevicePixelRatioChanges(t)}function createProxyAndListen(t,e,i){const s=t.canvas,a=throttled((e=>{null!==t.ctx&&i(fromNativeEvent(e,t))}),t);return addListener(s,e,a),a}class DomPlatform extends BasePlatform{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(initCanvas(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e.$chartjs)return!1;const i=e.$chartjs.initial;["height","width"].forEach((t=>{const s=i[t];isNullOrUndef(s)?e.removeAttribute(t):e.setAttribute(t,s)}));const s=i.style||{};return Object.keys(s).forEach((t=>{e.style[t]=s[t]})),e.width=e.width,delete e.$chartjs,!0}addEventListener(t,e,i){this.removeEventListener(t,e);const s=t.$proxies||(t.$proxies={}),a={attach:createAttachObserver,detach:createDetachObserver,resize:createResizeObserver}[e]||createProxyAndListen;s[e]=a(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:releaseObserver,detach:releaseObserver,resize:releaseObserver}[e]||removeListener)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return getMaximumSize(t,e,i,s)}isAttached(t){const e=t&&_getParentNode(t);return!(!e||!e.isConnected)}}function _detectPlatform(t){return!_isDomSupported()||"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?BasicPlatform:DomPlatform}class Element{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return isNumber(this.x)&&isNumber(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const s={};return t.forEach((t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]})),s}}function autoSkip(t,e){const i=t.options.ticks,s=determineMaxTicks(t),a=Math.min(i.maxTicksLimit||s,s),n=i.major.enabled?getMajorIndices(e):[],o=n.length,r=n[0],l=n[o-1],h=[];if(o>a)return skipMajors(e,h,n,o/a),h;const c=calculateSpacing(n,e,a);if(o>0){let t,i;const s=o>1?Math.round((l-r)/(o-1)):null;for(skip(e,h,c,isNullOrUndef(s)?0:r-s,r),t=0,i=o-1;t<i;t++)skip(e,h,c,n[t],n[t+1]);return skip(e,h,c,l,isNullOrUndef(s)?e.length:l+s),h}return skip(e,h,c),h}function determineMaxTicks(t){const e=t.options.offset,i=t._tickSize(),s=t._length/i+(e?0:1),a=t._maxLength/i;return Math.floor(Math.min(s,a))}function calculateSpacing(t,e,i){const s=getEvenSpacing(t),a=e.length/i;if(!s)return Math.max(a,1);const n=_factorize(s);for(let t=0,e=n.length-1;t<e;t++){const e=n[t];if(e>a)return e}return Math.max(a,1)}function getMajorIndices(t){const e=[];let i,s;for(i=0,s=t.length;i<s;i++)t[i].major&&e.push(i);return e}function skipMajors(t,e,i,s){let a,n=0,o=i[0];for(s=Math.ceil(s),a=0;a<t.length;a++)a===o&&(e.push(t[a]),n++,o=i[n*s])}function skip(t,e,i,s,a){const n=valueOrDefault(s,0),o=Math.min(valueOrDefault(a,t.length),t.length);let r,l,h,c=0;for(i=Math.ceil(i),a&&(r=a-s,i=r/Math.floor(r/i)),h=n;h<0;)c++,h=Math.round(n+c*i);for(l=Math.max(n,0);l<o;l++)l===h&&(e.push(t[l]),c++,h=Math.round(n+c*i))}function getEvenSpacing(t){const e=t.length;let i,s;if(e<2)return!1;for(s=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==s)return!1;return s}const reverseAlign=t=>"left"===t?"right":"right"===t?"left":t,offsetFromEdge=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,getTicksLimit=(t,e)=>Math.min(e||t,t);function sample(t,e){const i=[],s=t.length/e,a=t.length;let n=0;for(;n<a;n+=s)i.push(t[Math.floor(n)]);return i}function getPixelForGridLine(t,e,i){const s=t.ticks.length,a=Math.min(e,s-1),n=t._startPixel,o=t._endPixel,r=1e-6;let l,h=t.getPixelForTick(a);if(!(i&&(l=1===s?Math.max(h-n,o-h):0===e?(t.getPixelForTick(1)-h)/2:(h-t.getPixelForTick(a-1))/2,h+=a<e?l:-l,h<n-r||h>o+r)))return h}function garbageCollect(t,e){each(t,(t=>{const i=t.gc,s=i.length/2;let a;if(s>e){for(a=0;a<s;++a)delete t.data[i[a]];i.splice(0,s)}}))}function getTickMarkLength(t){return t.drawTicks?t.tickLength:0}function getTitleHeight(t,e){if(!t.display)return 0;const i=toFont(t.font,e),s=toPadding(t.padding);return(isArray(t.text)?t.text.length:1)*i.lineHeight+s.height}function createScaleContext(t,e){return createContext(t,{scale:e,type:"scale"})}function createTickContext(t,e,i){return createContext(t,{tick:i,index:e,type:"tick"})}function titleAlign(t,e,i){let s=_toLeftRightCenter(t);return(i&&"right"!==e||!i&&"right"===e)&&(s=reverseAlign(s)),s}function titleArgs(t,e,i,s){const{top:a,left:n,bottom:o,right:r,chart:l}=t,{chartArea:h,scales:c}=l;let d,u,g,p=0;const f=o-a,m=r-n;if(t.isHorizontal()){if(u=_alignStartEnd(s,n,r),isObject(i)){const t=Object.keys(i)[0],s=i[t];g=c[t].getPixelForValue(s)+f-e}else g="center"===i?(h.bottom+h.top)/2+f-e:offsetFromEdge(t,i,e);d=r-n}else{if(isObject(i)){const t=Object.keys(i)[0],s=i[t];u=c[t].getPixelForValue(s)-m+e}else u="center"===i?(h.left+h.right)/2-m+e:offsetFromEdge(t,i,e);g=_alignStartEnd(s,o,a),p="left"===i?-HALF_PI:HALF_PI}return{titleX:u,titleY:g,maxWidth:d,rotation:p}}class Scale extends Element{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=finiteOrDefault(t,Number.POSITIVE_INFINITY),e=finiteOrDefault(e,Number.NEGATIVE_INFINITY),i=finiteOrDefault(i,Number.POSITIVE_INFINITY),s=finiteOrDefault(s,Number.NEGATIVE_INFINITY),{min:finiteOrDefault(t,i),max:finiteOrDefault(e,s),minDefined:isNumberFinite(t),maxDefined:isNumberFinite(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:a,maxDefined:n}=this.getUserBounds();if(a&&n)return{min:i,max:s};const o=this.getMatchingVisibleMetas();for(let r=0,l=o.length;r<l;++r)e=o[r].controller.getMinMax(this,t),a||(i=Math.min(i,e.min)),n||(s=Math.max(s,e.max));return i=n&&i>s?s:i,s=a&&i>s?i:s,{min:finiteOrDefault(i,finiteOrDefault(s,i)),max:finiteOrDefault(s,finiteOrDefault(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){callback(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:s,grace:a,ticks:n}=this.options,o=n.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=_addGrace(this,a,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const r=o<this.ticks.length;this._convertTicksToLabels(r?sample(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),n.display&&(n.autoSkip||"auto"===n.source)&&(this.ticks=autoSkip(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),r&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){callback(this.options.afterUpdate,[this])}beforeSetDimensions(){callback(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){callback(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),callback(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){callback(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,s,a;for(i=0,s=t.length;i<s;i++)a=t[i],a.label=callback(e.callback,[a.value,i,t],this)}afterTickToLabelConversion(){callback(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){callback(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=getTicksLimit(this.ticks.length,t.ticks.maxTicksLimit),s=e.minRotation||0,a=e.maxRotation;let n,o,r,l=s;if(!this._isVisible()||!e.display||s>=a||i<=1||!this.isHorizontal())return void(this.labelRotation=s);const h=this._getLabelSizes(),c=h.widest.width,d=h.highest.height,u=_limitValue(this.chart.width-c,0,this.maxWidth);n=t.offset?this.maxWidth/i:u/(i-1),c+6>n&&(n=u/(i-(t.offset?.5:1)),o=this.maxHeight-getTickMarkLength(t.grid)-e.padding-getTitleHeight(t.title,this.chart.options.font),r=Math.sqrt(c*c+d*d),l=toDegrees(Math.min(Math.asin(_limitValue((h.highest.height+6)/n,-1,1)),Math.asin(_limitValue(o/r,-1,1))-Math.asin(_limitValue(d/r,-1,1)))),l=Math.max(s,Math.min(a,l))),this.labelRotation=l}afterCalculateLabelRotation(){callback(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){callback(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:a}}=this,n=this._isVisible(),o=this.isHorizontal();if(n){const n=getTitleHeight(s,e.options.font);if(o?(t.width=this.maxWidth,t.height=getTickMarkLength(a)+n):(t.height=this.maxHeight,t.width=getTickMarkLength(a)+n),i.display&&this.ticks.length){const{first:e,last:s,widest:a,highest:n}=this._getLabelSizes(),r=2*i.padding,l=toRadians(this.labelRotation),h=Math.cos(l),c=Math.sin(l);if(o){const e=i.mirror?0:c*a.width+h*n.height;t.height=Math.min(this.maxHeight,t.height+e+r)}else{const e=i.mirror?0:h*a.width+c*n.height;t.width=Math.min(this.maxWidth,t.width+e+r)}this._calculatePadding(e,s,c,h)}}this._handleMargins(),o?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){const{ticks:{align:a,padding:n},position:o}=this.options,r=0!==this.labelRotation,l="top"!==o&&"x"===this.axis;if(this.isHorizontal()){const o=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let c=0,d=0;r?l?(c=s*t.width,d=i*e.height):(c=i*t.height,d=s*e.width):"start"===a?d=e.width:"end"===a?c=t.width:"inner"!==a&&(c=t.width/2,d=e.width/2),this.paddingLeft=Math.max((c-o+n)*this.width/(this.width-o),0),this.paddingRight=Math.max((d-h+n)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===a?(i=0,s=t.height):"end"===a&&(i=e.height,s=0),this.paddingTop=i+n,this.paddingBottom=s+n}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){callback(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)isNullOrUndef(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=sample(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:s,_longestTextCache:a}=this,n=[],o=[],r=Math.floor(e/getTicksLimit(e,i));let l,h,c,d,u,g,p,f,m,x,b,_=0,y=0;for(l=0;l<e;l+=r){if(d=t[l].label,u=this._resolveTickFontOptions(l),s.font=g=u.string,p=a[g]=a[g]||{data:{},gc:[]},f=u.lineHeight,m=x=0,isNullOrUndef(d)||isArray(d)){if(isArray(d))for(h=0,c=d.length;h<c;++h)b=d[h],isNullOrUndef(b)||isArray(b)||(m=_measureText(s,p.data,p.gc,m,b),x+=f)}else m=_measureText(s,p.data,p.gc,m,d),x=f;n.push(m),o.push(x),_=Math.max(m,_),y=Math.max(x,y)}garbageCollect(a,e);const v=n.indexOf(_),k=o.indexOf(y),S=t=>({width:n[t]||0,height:o[t]||0});return{first:S(0),last:S(e-1),widest:S(v),highest:S(k),widths:n,heights:o}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return _int16Range(this._alignToPixels?_alignPixel(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=createTickContext(this.getContext(),t,i))}return this.$context||(this.$context=createScaleContext(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=toRadians(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),a=this._getLabelSizes(),n=t.autoSkipPadding||0,o=a?a.widest.width+n:0,r=a?a.highest.height+n:0;return this.isHorizontal()?r*i>o*s?o/i:r/s:r*s<o*i?r/i:o/s}_isVisible(){const t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,s=this.options,{grid:a,position:n,border:o}=s,r=a.offset,l=this.isHorizontal(),h=this.ticks.length+(r?1:0),c=getTickMarkLength(a),d=[],u=o.setContext(this.getContext()),g=u.display?u.width:0,p=g/2,f=function(t){return _alignPixel(i,t,g)};let m,x,b,_,y,v,k,S,D,M,P,A;if("top"===n)m=f(this.bottom),v=this.bottom-c,S=m-p,M=f(t.top)+p,A=t.bottom;else if("bottom"===n)m=f(this.top),M=t.top,A=f(t.bottom)-p,v=m+p,S=this.top+c;else if("left"===n)m=f(this.right),y=this.right-c,k=m-p,D=f(t.left)+p,P=t.right;else if("right"===n)m=f(this.left),D=t.left,P=f(t.right)-p,y=m+p,k=this.left+c;else if("x"===e){if("center"===n)m=f((t.top+t.bottom)/2+.5);else if(isObject(n)){const t=Object.keys(n)[0],e=n[t];m=f(this.chart.scales[t].getPixelForValue(e))}M=t.top,A=t.bottom,v=m+p,S=v+c}else if("y"===e){if("center"===n)m=f((t.left+t.right)/2);else if(isObject(n)){const t=Object.keys(n)[0],e=n[t];m=f(this.chart.scales[t].getPixelForValue(e))}y=m-p,k=y-c,D=t.left,P=t.right}const C=valueOrDefault(s.ticks.maxTicksLimit,h),w=Math.max(1,Math.ceil(h/C));for(x=0;x<h;x+=w){const t=this.getContext(x),e=a.setContext(t),s=o.setContext(t),n=e.lineWidth,h=e.color,c=s.dash||[],u=s.dashOffset,g=e.tickWidth,p=e.tickColor,f=e.tickBorderDash||[],m=e.tickBorderDashOffset;b=getPixelForGridLine(this,x,r),void 0!==b&&(_=_alignPixel(i,b,n),l?y=k=D=P=_:v=S=M=A=_,d.push({tx1:y,ty1:v,tx2:k,ty2:S,x1:D,y1:M,x2:P,y2:A,width:n,color:h,borderDash:c,borderDashOffset:u,tickWidth:g,tickColor:p,tickBorderDash:f,tickBorderDashOffset:m}))}return this._ticksLength=h,this._borderValue=m,d}_computeLabelItems(t){const e=this.axis,i=this.options,{position:s,ticks:a}=i,n=this.isHorizontal(),o=this.ticks,{align:r,crossAlign:l,padding:h,mirror:c}=a,d=getTickMarkLength(i.grid),u=d+h,g=c?-h:u,p=-toRadians(this.labelRotation),f=[];let m,x,b,_,y,v,k,S,D,M,P,A,C="middle";if("top"===s)v=this.bottom-g,k=this._getXAxisLabelAlignment();else if("bottom"===s)v=this.top+g,k=this._getXAxisLabelAlignment();else if("left"===s){const t=this._getYAxisLabelAlignment(d);k=t.textAlign,y=t.x}else if("right"===s){const t=this._getYAxisLabelAlignment(d);k=t.textAlign,y=t.x}else if("x"===e){if("center"===s)v=(t.top+t.bottom)/2+u;else if(isObject(s)){const t=Object.keys(s)[0],e=s[t];v=this.chart.scales[t].getPixelForValue(e)+u}k=this._getXAxisLabelAlignment()}else if("y"===e){if("center"===s)y=(t.left+t.right)/2-u;else if(isObject(s)){const t=Object.keys(s)[0],e=s[t];y=this.chart.scales[t].getPixelForValue(e)}k=this._getYAxisLabelAlignment(d).textAlign}"y"===e&&("start"===r?C="top":"end"===r&&(C="bottom"));const w=this._getLabelSizes();for(m=0,x=o.length;m<x;++m){b=o[m],_=b.label;const t=a.setContext(this.getContext(m));S=this.getPixelForTick(m)+a.labelOffset,D=this._resolveTickFontOptions(m),M=D.lineHeight,P=isArray(_)?_.length:1;const e=P/2,i=t.color,r=t.textStrokeColor,h=t.textStrokeWidth;let d,u=k;if(n?(y=S,"inner"===k&&(u=m===x-1?this.options.reverse?"left":"right":0===m?this.options.reverse?"right":"left":"center"),A="top"===s?"near"===l||0!==p?-P*M+M/2:"center"===l?-w.highest.height/2-e*M+M:-w.highest.height+M/2:"near"===l||0!==p?M/2:"center"===l?w.highest.height/2-e*M:w.highest.height-P*M,c&&(A*=-1),0===p||t.showLabelBackdrop||(y+=M/2*Math.sin(p))):(v=S,A=(1-P)*M/2),t.showLabelBackdrop){const e=toPadding(t.backdropPadding),i=w.heights[m],s=w.widths[m];let a=A-e.top,n=0-e.left;switch(C){case"middle":a-=i/2;break;case"bottom":a-=i}switch(k){case"center":n-=s/2;break;case"right":n-=s;break;case"inner":m===x-1?n-=s:m>0&&(n-=s/2)}d={left:n,top:a,width:s+e.width,height:i+e.height,color:t.backdropColor}}f.push({label:_,font:D,textOffset:A,options:{rotation:p,color:i,strokeColor:r,strokeWidth:h,textAlign:u,textBaseline:C,translation:[y,v],backdrop:d}})}return f}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-toRadians(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:s,padding:a}}=this.options,n=t+a,o=this._getLabelSizes().widest.width;let r,l;return"left"===e?s?(l=this.right+a,"near"===i?r="left":"center"===i?(r="center",l+=o/2):(r="right",l+=o)):(l=this.right-n,"near"===i?r="right":"center"===i?(r="center",l-=o/2):(r="left",l=this.left)):"right"===e?s?(l=this.left+a,"near"===i?r="right":"center"===i?(r="center",l-=o/2):(r="left",l-=o)):(l=this.left+n,"near"===i?r="left":"center"===i?(r="center",l+=o/2):(r="right",l=this.right)):r="right",{textAlign:r,x:l}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:s,width:a,height:n}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,a,n),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const i=this.ticks.findIndex((e=>e.value===t));if(i>=0){return e.setContext(this.getContext(i)).lineWidth}return 0}drawGrid(t){const e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let a,n;const o=(t,e,s)=>{s.width&&s.color&&(i.save(),i.lineWidth=s.width,i.strokeStyle=s.color,i.setLineDash(s.borderDash||[]),i.lineDashOffset=s.borderDashOffset,i.beginPath(),i.moveTo(t.x,t.y),i.lineTo(e.x,e.y),i.stroke(),i.restore())};if(e.display)for(a=0,n=s.length;a<n;++a){const t=s[a];e.drawOnChartArea&&o({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),e.drawTicks&&o({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:s}}=this,a=i.setContext(this.getContext()),n=i.display?a.width:0;if(!n)return;const o=s.setContext(this.getContext(0)).lineWidth,r=this._borderValue;let l,h,c,d;this.isHorizontal()?(l=_alignPixel(t,this.left,n)-n/2,h=_alignPixel(t,this.right,o)+o/2,c=d=r):(c=_alignPixel(t,this.top,n)-n/2,d=_alignPixel(t,this.bottom,o)+o/2,l=h=r),e.save(),e.lineWidth=a.width,e.strokeStyle=a.color,e.beginPath(),e.moveTo(l,c),e.lineTo(h,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const e=this.ctx,i=this._computeLabelArea();i&&clipArea(e,i);const s=this.getLabelItems(t);for(const t of s){const i=t.options,s=t.font,a=t.label,n=t.textOffset;renderText(e,a,0,n,s,i)}i&&unclipArea(e)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;const a=toFont(i.font),n=toPadding(i.padding),o=i.align;let r=a.lineHeight/2;"bottom"===e||"center"===e||isObject(e)?(r+=n.bottom,isArray(i.text)&&(r+=a.lineHeight*(i.text.length-1))):r+=n.top;const{titleX:l,titleY:h,maxWidth:c,rotation:d}=titleArgs(this,r,e,o);renderText(t,i.text,0,0,a,{color:i.color,maxWidth:c,rotation:d,textAlign:titleAlign(o,e,s),textBaseline:"middle",translation:[l,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=valueOrDefault(t.grid&&t.grid.z,-1),s=valueOrDefault(t.border&&t.border.z,0);return this._isVisible()&&this.draw===Scale.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let a,n;for(a=0,n=e.length;a<n;++a){const n=e[a];n[i]!==this.id||t&&n.type!==t||s.push(n)}return s}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return toFont(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class TypedRegistry{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;isIChartComponent(e)&&(i=this.register(e));const s=this.items,a=t.id,n=this.scope+"."+a;if(!a)throw new Error("class does not have id: "+t);return a in s||(s[a]=t,registerDefaults(t,n,i),this.override&&defaults.override(t.id,t.overrides)),n}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in defaults[s]&&(delete defaults[s][i],this.override&&delete overrides[i])}}function registerDefaults(t,e,i){const s=merge(Object.create(null),[i?defaults.get(i):{},defaults.get(e),t.defaults]);defaults.set(e,s),t.defaultRoutes&&routeDefaults(e,t.defaultRoutes),t.descriptors&&defaults.describe(e,t.descriptors)}function routeDefaults(t,e){Object.keys(e).forEach((i=>{const s=i.split("."),a=s.pop(),n=[t].concat(s).join("."),o=e[i].split("."),r=o.pop(),l=o.join(".");defaults.route(n,a,l,r)}))}function isIChartComponent(t){return"id"in t&&"defaults"in t}class Registry{constructor(){this.controllers=new TypedRegistry(DatasetController,"datasets",!0),this.elements=new TypedRegistry(Element,"elements"),this.plugins=new TypedRegistry(Object,"plugins"),this.scales=new TypedRegistry(Scale,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach((e=>{const s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):each(e,(e=>{const s=i||this._getRegistryForType(e);this._exec(t,s,e)}))}))}_exec(t,e,i){const s=_capitalize(t);callback(i["before"+s],[],i),e[t](i),callback(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const s=e.get(t);if(void 0===s)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var registry=new Registry;class PluginService{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const a=s?this._descriptors(t).filter(s):this._descriptors(t),n=this._notify(a,t,e,i);return"afterDestroy"===e&&(this._notify(a,t,"stop"),this._notify(this._init,t,"uninstall")),n}_notify(t,e,i,s){s=s||{};for(const a of t){const t=a.plugin,n=t[i],o=[e,s,a.options];if(!1===callback(n,o,t)&&s.cancelable)return!1}return!0}invalidate(){isNullOrUndef(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,s=valueOrDefault(i.options&&i.options.plugins,{}),a=allPlugins(i);return!1!==s||e?createDescriptors(t,a,s,e):[]}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter((t=>!e.some((e=>t.plugin.id===e.plugin.id))));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function allPlugins(t){const e={},i=[],s=Object.keys(registry.plugins.items);for(let t=0;t<s.length;t++)i.push(registry.getPlugin(s[t]));const a=t.plugins||[];for(let t=0;t<a.length;t++){const s=a[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}function getOpts(t,e){return e||!1!==t?!0===t?{}:t:null}function createDescriptors(t,{plugins:e,localIds:i},s,a){const n=[],o=t.getContext();for(const r of e){const e=r.id,l=getOpts(s[e],a);null!==l&&n.push({plugin:r,options:pluginOpts(t.config,{plugin:r,local:i[e]},l,o)})}return n}function pluginOpts(t,{plugin:e,local:i},s,a){const n=t.pluginScopeKeys(e),o=t.getOptionScopes(s,n);return i&&e.defaults&&o.push(e.defaults),t.createResolver(o,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function getIndexAxis(t,e){const i=defaults.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function getAxisFromDefaultScaleID(t,e){let i=t;return"_index_"===t?i=e:"_value_"===t&&(i="x"===e?"y":"x"),i}function getDefaultScaleIDFromAxis(t,e){return t===e?"_index_":"_value_"}function idMatchesAxis(t){if("x"===t||"y"===t||"r"===t)return t}function axisFromPosition(t){return"top"===t||"bottom"===t?"x":"left"===t||"right"===t?"y":void 0}function determineAxis(t,...e){if(idMatchesAxis(t))return t;for(const i of e){const e=i.axis||axisFromPosition(i.position)||t.length>1&&idMatchesAxis(t[0].toLowerCase());if(e)return e}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function getAxisFromDataset(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function retrieveAxisFromDatasets(t,e){if(e.data&&e.data.datasets){const i=e.data.datasets.filter((e=>e.xAxisID===t||e.yAxisID===t));if(i.length)return getAxisFromDataset(t,"x",i[0])||getAxisFromDataset(t,"y",i[0])}return{}}function mergeScaleConfig(t,e){const i=overrides[t.type]||{scales:{}},s=e.scales||{},a=getIndexAxis(t.type,e),n=Object.create(null);return Object.keys(s).forEach((e=>{const o=s[e];if(!isObject(o))return console.error(`Invalid scale configuration for scale: ${e}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);const r=determineAxis(e,o,retrieveAxisFromDatasets(e,t),defaults.scales[o.type]),l=getDefaultScaleIDFromAxis(r,a),h=i.scales||{};n[e]=mergeIf(Object.create(null),[{axis:r},o,h[r],h[l]])})),t.data.datasets.forEach((i=>{const a=i.type||t.type,o=i.indexAxis||getIndexAxis(a,e),r=(overrides[a]||{}).scales||{};Object.keys(r).forEach((t=>{const e=getAxisFromDefaultScaleID(t,o),a=i[e+"AxisID"]||e;n[a]=n[a]||Object.create(null),mergeIf(n[a],[{axis:e},s[a],r[t]])}))})),Object.keys(n).forEach((t=>{const e=n[t];mergeIf(e,[defaults.scales[e.type],defaults.scale])})),n}function initOptions(t){const e=t.options||(t.options={});e.plugins=valueOrDefault(e.plugins,{}),e.scales=mergeScaleConfig(t,e)}function initData(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}function initConfig(t){return(t=t||{}).data=initData(t.data),initOptions(t),t}const keyCache=new Map,keysCached=new Set;function cachedKeys(t,e){let i=keyCache.get(t);return i||(i=e(),keyCache.set(t,i),keysCached.add(i)),i}const addIfFound=(t,e,i)=>{const s=resolveObjectKey(e,i);void 0!==s&&t.add(s)};class Config{constructor(t){this._config=initConfig(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=initData(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),initOptions(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return cachedKeys(t,(()=>[[`datasets.${t}`,""]]))}datasetAnimationScopeKeys(t,e){return cachedKeys(`${t}.transition.${e}`,(()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]]))}datasetElementScopeKeys(t,e){return cachedKeys(`${t}-${e}`,(()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]]))}pluginScopeKeys(t){const e=t.id;return cachedKeys(`${this.type}-plugin-${e}`,(()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]]))}_cachedScopes(t,e){const i=this._scopeCache;let s=i.get(t);return s&&!e||(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){const{options:s,type:a}=this,n=this._cachedScopes(t,i),o=n.get(e);if(o)return o;const r=new Set;e.forEach((e=>{t&&(r.add(t),e.forEach((e=>addIfFound(r,t,e)))),e.forEach((t=>addIfFound(r,s,t))),e.forEach((t=>addIfFound(r,overrides[a]||{},t))),e.forEach((t=>addIfFound(r,defaults,t))),e.forEach((t=>addIfFound(r,descriptors,t)))}));const l=Array.from(r);return 0===l.length&&l.push(Object.create(null)),keysCached.has(e)&&n.set(e,l),l}chartOptionScopes(){const{options:t,type:e}=this;return[t,overrides[e]||{},defaults.datasets[e]||{},{type:e},defaults,descriptors]}resolveNamedOptions(t,e,i,s=[""]){const a={$shared:!0},{resolver:n,subPrefixes:o}=getResolver(this._resolverCache,t,s);let r=n;if(needContext(n,e)){a.$shared=!1,i=isFunction(i)?i():i;const e=this.createResolver(t,i,o);r=_attachContext(n,i,e)}for(const t of e)a[t]=r[t];return a}createResolver(t,e,i=[""],s){const{resolver:a}=getResolver(this._resolverCache,t,i);return isObject(e)?_attachContext(a,e,void 0,s):a}}function getResolver(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));const a=i.join();let n=s.get(a);if(!n){n={resolver:_createResolver(e,i),subPrefixes:i.filter((t=>!t.toLowerCase().includes("hover")))},s.set(a,n)}return n}const hasFunction=t=>isObject(t)&&Object.getOwnPropertyNames(t).some((e=>isFunction(t[e])));function needContext(t,e){const{isScriptable:i,isIndexable:s}=_descriptors(t);for(const a of e){const e=i(a),n=s(a),o=(n||e)&&t[a];if(e&&(isFunction(o)||hasFunction(o))||n&&isArray(o))return!0}return!1}var version="4.5.0";const KNOWN_POSITIONS=["top","bottom","left","right","chartArea"];function positionIsHorizontal(t,e){return"top"===t||"bottom"===t||-1===KNOWN_POSITIONS.indexOf(t)&&"x"===e}function compare2Level(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function onAnimationsComplete(t){const e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),callback(i&&i.onComplete,[t],e)}function onAnimationProgress(t){const e=t.chart,i=e.options.animation;callback(i&&i.onProgress,[t],e)}function getCanvas(t){return _isDomSupported()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const instances={},getChart=t=>{const e=getCanvas(t);return Object.values(instances).filter((t=>t.canvas===e)).pop()};function moveNumericKeys(t,e,i){const s=Object.keys(t);for(const a of s){const s=+a;if(s>=e){const n=t[a];delete t[a],(i>0||s>e)&&(t[s+i]=n)}}}function determineLastEvent(t,e,i,s){return i&&"mouseout"!==t.type?s?e:t:null}class Chart{static defaults=defaults;static instances=instances;static overrides=overrides;static registry=registry;static version=version;static getChart=getChart;static register(...t){registry.add(...t),invalidatePlugins()}static unregister(...t){registry.remove(...t),invalidatePlugins()}constructor(t,e){const i=this.config=new Config(e),s=getCanvas(t),a=getChart(s);if(a)throw new Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");const n=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||_detectPlatform(s)),this.platform.updateConfig(i);const o=this.platform.acquireContext(s,n.aspectRatio),r=o&&o.canvas,l=r&&r.height,h=r&&r.width;this.id=uid(),this.ctx=o,this.canvas=r,this.width=h,this.height=l,this._options=n,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new PluginService,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=debounce((t=>this.update(t)),n.resizeDelay||0),this._dataChanges=[],instances[this.id]=this,o&&r?(animator.listen(this,"complete",onAnimationsComplete),animator.listen(this,"progress",onAnimationProgress),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:a}=this;return isNullOrUndef(t)?e&&a?a:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return registry}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():retinaScale(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return clearCanvas(this.canvas,this.ctx),this}stop(){return animator.stop(this),this}resize(t,e){animator.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,s=this.canvas,a=i.maintainAspectRatio&&this.aspectRatio,n=this.platform.getMaximumSize(s,t,e,a),o=i.devicePixelRatio||this.platform.getDevicePixelRatio(),r=this.width?"resize":"attach";this.width=n.width,this.height=n.height,this._aspectRatio=this.aspectRatio,retinaScale(this,o,!0)&&(this.notifyPlugins("resize",{size:n}),callback(i.onResize,[this,n],this),this.attached&&this._doResize(r)&&this.render())}ensureScalesHaveIDs(){const t=this.options.scales||{};each(t,((t,e)=>{t.id=e}))}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce(((t,e)=>(t[e]=!1,t)),{});let a=[];e&&(a=a.concat(Object.keys(e).map((t=>{const i=e[t],s=determineAxis(t,i),a="r"===s,n="x"===s;return{options:i,dposition:a?"chartArea":n?"bottom":"left",dtype:a?"radialLinear":n?"category":"linear"}})))),each(a,(e=>{const a=e.options,n=a.id,o=determineAxis(n,a),r=valueOrDefault(a.type,e.dtype);void 0!==a.position&&positionIsHorizontal(a.position,o)===positionIsHorizontal(e.dposition)||(a.position=e.dposition),s[n]=!0;let l=null;if(n in i&&i[n].type===r)l=i[n];else{l=new(registry.getScale(r))({id:n,type:r,ctx:this.ctx,chart:this}),i[l.id]=l}l.init(a,t)})),each(s,((t,e)=>{t||delete i[e]})),each(i,(t=>{layouts.configure(this,t,t.options),layouts.addBox(this,t)}))}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort(((t,e)=>t.index-e.index)),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(compare2Level("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach(((t,i)=>{0===e.filter((e=>e===t._dataset)).length&&this._destroyDatasetMeta(i)}))}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){const s=e[i];let a=this.getDatasetMeta(i);const n=s.type||this.config.type;if(a.type&&a.type!==n&&(this._destroyDatasetMeta(i),a=this.getDatasetMeta(i)),a.type=n,a.indexAxis=s.indexAxis||getIndexAxis(n,this.options),a.order=s.order||0,a.index=i,a.label=""+s.label,a.visible=this.isDatasetVisible(i),a.controller)a.controller.updateIndex(i),a.controller.linkScales();else{const e=registry.getController(n),{datasetElementType:s,dataElementType:o}=defaults.datasets[n];Object.assign(e,{dataElementType:registry.getElement(o),datasetElementType:s&&registry.getElement(s)}),a.controller=new e(this,i),t.push(a.controller)}}return this._updateMetasets(),t}_resetElements(){each(this.data.datasets,((t,e)=>{this.getDatasetMeta(e).controller.reset()}),this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let n=0;for(let t=0,e=this.data.datasets.length;t<e;t++){const{controller:e}=this.getDatasetMeta(t),i=!s&&-1===a.indexOf(e);e.buildOrUpdateElements(i),n=Math.max(+e.getMaxOverflow(),n)}n=this._minPadding=i.layout.autoPadding?n:0,this._updateLayout(n),s||each(a,(t=>{t.reset()})),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(compare2Level("z","_idx"));const{_active:o,_lastEvent:r}=this;r?this._eventHandler(r,!0):o.length&&this._updateHoverStyles(o,o,!0),this.render()}_updateScales(){each(this.scales,(t=>{layouts.removeBox(this,t)})),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);setsEqual(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:a}of e){moveNumericKeys(t,s,"_removeElements"===i?-a:a)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=e=>new Set(t.filter((t=>t[0]===e)).map(((t,e)=>e+","+t.splice(1).join(",")))),s=i(0);for(let t=1;t<e;t++)if(!setsEqual(s,i(t)))return;return Array.from(s).map((t=>t.split(","))).map((t=>({method:t[1],start:+t[2],count:+t[3]})))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;layouts.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],each(this.boxes,(t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))}),this),this._layers.forEach(((t,e)=>{t._idx=e})),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,isFunction(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(animator.has(this)?this.attached&&!animator.running(this)&&animator.start(this):(this.draw(),onAnimationsComplete({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0)return;if(!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let s,a;for(s=0,a=e.length;s<a;++s){const a=e[s];t&&!a.visible||i.push(a)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=getDatasetClipArea(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&clipArea(e,s),t.controller.draw(),s&&unclipArea(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return _isPointInArea(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){const a=Interaction.modes[e];return"function"==typeof a?a(this,t,i,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let s=i.filter((t=>t&&t._dataset===e)).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=createContext(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const s=i?"show":"hide",a=this.getDatasetMeta(t),n=a.controller._resolveAnimations(void 0,s);defined(e)?(a.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),n.update(a,{visible:i}),this.update((e=>e.datasetIndex===t?s:void 0)))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),animator.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),clearCanvas(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete instances[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};each(this.options.events,(t=>i(t,s)))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(i,s)=>{t[i]&&(e.removeEventListener(this,i,s),delete t[i])},a=(t,e)=>{this.canvas&&this.resize(t,e)};let n;const o=()=>{s("attach",o),this.attached=!0,this.resize(),i("resize",a),i("detach",n)};n=()=>{this.attached=!1,s("resize",a),this._stop(),this._resize(0,0),i("attach",o)},e.isAttached(this.canvas)?o():n()}unbindEvents(){each(this._listeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._listeners={},each(this._responsiveListeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const s=i?"set":"remove";let a,n,o,r;for("dataset"===e&&(a=this.getDatasetMeta(t[0].datasetIndex),a.controller["_"+s+"DatasetHoverStyle"]()),o=0,r=t.length;o<r;++o){n=t[o];const e=n&&this.getDatasetMeta(n.datasetIndex).controller;e&&e[s+"HoverStyle"](n.element,n.datasetIndex,n.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map((({datasetIndex:t,index:e})=>{const i=this.getDatasetMeta(t);if(!i)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}));!_elementsEqual(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter((e=>e.plugin.id===t)).length}_updateHoverStyles(t,e,i){const s=this.options.hover,a=(t,e)=>t.filter((t=>!e.some((e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)))),n=a(e,t),o=i?t:a(t,e);n.length&&this.updateHoverStyle(n,s.mode,!1),o.length&&s.mode&&this.updateHoverStyle(o,s.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;const a=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(a||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:s=[],options:a}=this,n=e,o=this._getActiveElements(t,s,i,n),r=_isClickEvent(t),l=determineLastEvent(t,this._lastEvent,i,r);i&&(this._lastEvent=null,callback(a.onHover,[t,o,this],this),r&&callback(a.onClick,[t,o,this],this));const h=!_elementsEqual(o,s);return(h||e)&&(this._active=o,this._updateHoverStyles(o,s,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;const a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,s)}}function invalidatePlugins(){return each(Chart.instances,(t=>t._plugins.invalidate()))}function clipSelf(t,e,i){const{startAngle:s,x:a,y:n,outerRadius:o,innerRadius:r,options:l}=e,{borderWidth:h,borderJoinStyle:c}=l,d=Math.min(h/o,_normalizeAngle(s-i));if(t.beginPath(),t.arc(a,n,o-h/2,s+d/2,i-d/2),r>0){const e=Math.min(h/r,_normalizeAngle(s-i));t.arc(a,n,r+h/2,i-e/2,s+e/2,!0)}else{const e=Math.min(h/2,o*_normalizeAngle(s-i));if("round"===c)t.arc(a,n,e,i-PI/2,s+PI/2,!0);else if("bevel"===c){const o=2*e*e,r=-o*Math.cos(i+PI/2)+a,l=-o*Math.sin(i+PI/2)+n,h=o*Math.cos(s+PI/2)+a,c=o*Math.sin(s+PI/2)+n;t.lineTo(r,l),t.lineTo(h,c)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}function clipArc(t,e,i){const{startAngle:s,pixelMargin:a,x:n,y:o,outerRadius:r,innerRadius:l}=e;let h=a/r;t.beginPath(),t.arc(n,o,r,s-h,i+h),l>a?(h=a/l,t.arc(n,o,l,i+h,s-h,!0)):t.arc(n,o,a,i+HALF_PI,s-HALF_PI),t.closePath(),t.clip()}function toRadiusCorners(t){return _readValueToProps(t,["outerStart","outerEnd","innerStart","innerEnd"])}function parseBorderRadius$1(t,e,i,s){const a=toRadiusCorners(t.options.borderRadius),n=(i-e)/2,o=Math.min(n,s*e/2),r=t=>{const e=(i-Math.min(n,t))*s/2;return _limitValue(t,0,Math.min(n,e))};return{outerStart:r(a.outerStart),outerEnd:r(a.outerEnd),innerStart:_limitValue(a.innerStart,0,o),innerEnd:_limitValue(a.innerEnd,0,o)}}function rThetaToXY(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function pathArc(t,e,i,s,a,n){const{x:o,y:r,startAngle:l,pixelMargin:h,innerRadius:c}=e,d=Math.max(e.outerRadius+s+i-h,0),u=c>0?c+s+i+h:0;let g=0;const p=a-l;if(s){const t=((c>0?c-s:0)+(d>0?d-s:0))/2;g=(p-(0!==t?p*t/(t+s):p))/2}const f=(p-Math.max(.001,p*d-i/PI)/d)/2,m=l+f+g,x=a-f-g,{outerStart:b,outerEnd:_,innerStart:y,innerEnd:v}=parseBorderRadius$1(e,u,d,x-m),k=d-b,S=d-_,D=m+b/k,M=x-_/S,P=u+y,A=u+v,C=m+y/P,w=x-v/A;if(t.beginPath(),n){const e=(D+M)/2;if(t.arc(o,r,d,D,e),t.arc(o,r,d,e,M),_>0){const e=rThetaToXY(S,M,o,r);t.arc(e.x,e.y,_,M,x+HALF_PI)}const i=rThetaToXY(A,x,o,r);if(t.lineTo(i.x,i.y),v>0){const e=rThetaToXY(A,w,o,r);t.arc(e.x,e.y,v,x+HALF_PI,w+Math.PI)}const s=(x-v/u+(m+y/u))/2;if(t.arc(o,r,u,x-v/u,s,!0),t.arc(o,r,u,s,m+y/u,!0),y>0){const e=rThetaToXY(P,C,o,r);t.arc(e.x,e.y,y,C+Math.PI,m-HALF_PI)}const a=rThetaToXY(k,m,o,r);if(t.lineTo(a.x,a.y),b>0){const e=rThetaToXY(k,D,o,r);t.arc(e.x,e.y,b,m-HALF_PI,D)}}else{t.moveTo(o,r);const e=Math.cos(D)*d+o,i=Math.sin(D)*d+r;t.lineTo(e,i);const s=Math.cos(M)*d+o,a=Math.sin(M)*d+r;t.lineTo(s,a)}t.closePath()}function drawArc(t,e,i,s,a){const{fullCircles:n,startAngle:o,circumference:r}=e;let l=e.endAngle;if(n){pathArc(t,e,i,s,l,a);for(let e=0;e<n;++e)t.fill();isNaN(r)||(l=o+(r%TAU||TAU))}return pathArc(t,e,i,s,l,a),t.fill(),l}function drawBorder(t,e,i,s,a){const{fullCircles:n,startAngle:o,circumference:r,options:l}=e,{borderWidth:h,borderJoinStyle:c,borderDash:d,borderDashOffset:u,borderRadius:g}=l,p="inner"===l.borderAlign;if(!h)return;t.setLineDash(d||[]),t.lineDashOffset=u,p?(t.lineWidth=2*h,t.lineJoin=c||"round"):(t.lineWidth=h,t.lineJoin=c||"bevel");let f=e.endAngle;if(n){pathArc(t,e,i,s,f,a);for(let e=0;e<n;++e)t.stroke();isNaN(r)||(f=o+(r%TAU||TAU))}p&&clipArc(t,e,f),l.selfJoin&&f-o>=PI&&0===g&&"miter"!==c&&clipSelf(t,e,f),n||(pathArc(t,e,i,s,f,a),t.stroke())}class ArcElement extends Element{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){const s=this.getProps(["x","y"],i),{angle:a,distance:n}=getAngleFromPoint(s,{x:t,y:e}),{startAngle:o,endAngle:r,innerRadius:l,outerRadius:h,circumference:c}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,u=valueOrDefault(c,r-o),g=_angleBetween(a,o,r)&&o!==r,p=u>=TAU||g,f=_isBetween(n,l+d,h+d);return p&&f}getCenterPoint(t){const{x:e,y:i,startAngle:s,endAngle:a,innerRadius:n,outerRadius:o}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:r,spacing:l}=this.options,h=(s+a)/2,c=(n+o+l+r)/2;return{x:e+Math.cos(h)*c,y:i+Math.sin(h)*c}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:i}=this,s=(e.offset||0)/4,a=(e.spacing||0)/2,n=e.circular;if(this.pixelMargin="inner"===e.borderAlign?.33:0,this.fullCircles=i>TAU?Math.floor(i/TAU):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();const o=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(o)*s,Math.sin(o)*s);const r=s*(1-Math.sin(Math.min(PI,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,drawArc(t,this,r,a,n),drawBorder(t,this,r,a,n),t.restore()}}function setStyle(t,e,i=e){t.lineCap=valueOrDefault(i.borderCapStyle,e.borderCapStyle),t.setLineDash(valueOrDefault(i.borderDash,e.borderDash)),t.lineDashOffset=valueOrDefault(i.borderDashOffset,e.borderDashOffset),t.lineJoin=valueOrDefault(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=valueOrDefault(i.borderWidth,e.borderWidth),t.strokeStyle=valueOrDefault(i.borderColor,e.borderColor)}function lineTo(t,e,i){t.lineTo(i.x,i.y)}function getLineMethod(t){return t.stepped?_steppedLineTo:t.tension||"monotone"===t.cubicInterpolationMode?_bezierCurveTo:lineTo}function pathVars(t,e,i={}){const s=t.length,{start:a=0,end:n=s-1}=i,{start:o,end:r}=e,l=Math.max(a,o),h=Math.min(n,r),c=a<o&&n<o||a>r&&n>r;return{count:s,start:l,loop:e.loop,ilen:h<l&&!c?s+h-l:h-l}}function pathSegment(t,e,i,s){const{points:a,options:n}=e,{count:o,start:r,loop:l,ilen:h}=pathVars(a,i,s),c=getLineMethod(n);let d,u,g,{move:p=!0,reverse:f}=s||{};for(d=0;d<=h;++d)u=a[(r+(f?h-d:d))%o],u.skip||(p?(t.moveTo(u.x,u.y),p=!1):c(t,g,u,f,n.stepped),g=u);return l&&(u=a[(r+(f?h:0))%o],c(t,g,u,f,n.stepped)),!!l}function fastPathSegment(t,e,i,s){const a=e.points,{count:n,start:o,ilen:r}=pathVars(a,i,s),{move:l=!0,reverse:h}=s||{};let c,d,u,g,p,f,m=0,x=0;const b=t=>(o+(h?r-t:t))%n,_=()=>{g!==p&&(t.lineTo(m,p),t.lineTo(m,g),t.lineTo(m,f))};for(l&&(d=a[b(0)],t.moveTo(d.x,d.y)),c=0;c<=r;++c){if(d=a[b(c)],d.skip)continue;const e=d.x,i=d.y,s=0|e;s===u?(i<g?g=i:i>p&&(p=i),m=(x*m+e)/++x):(_(),t.lineTo(e,i),u=s,x=0,g=p=i),f=i}_()}function _getSegmentMethod(t){const e=t.options,i=e.borderDash&&e.borderDash.length;return!(t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i)?fastPathSegment:pathSegment}function _getInterpolationMethod(t){return t.stepped?_steppedInterpolation:t.tension||"monotone"===t.cubicInterpolationMode?_bezierInterpolation:_pointInLine}function strokePathWithCache(t,e,i,s){let a=e._path;a||(a=e._path=new Path2D,e.path(a,i,s)&&a.closePath()),setStyle(t,e.options),t.stroke(a)}function strokePathDirect(t,e,i,s){const{segments:a,options:n}=e,o=_getSegmentMethod(e);for(const r of a)setStyle(t,n,r.style),t.beginPath(),o(t,e,r,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}const usePath2D="function"==typeof Path2D;function draw(t,e,i,s){usePath2D&&!e.options.segment?strokePathWithCache(t,e,i,s):strokePathDirect(t,e,i,s)}class LineElement extends Element{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;_updateBezierControlPoints(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=_computeSegments(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,s=t[e],a=this.points,n=_boundSegments(this,{property:e,start:s,end:s});if(!n.length)return;const o=[],r=_getInterpolationMethod(i);let l,h;for(l=0,h=n.length;l<h;++l){const{start:h,end:c}=n[l],d=a[h],u=a[c];if(d===u){o.push(d);continue}const g=r(d,u,Math.abs((s-d[e])/(u[e]-d[e])),i.stepped);g[e]=t[e],o.push(g)}return 1===o.length?o[0]:o}pathSegment(t,e,i){return _getSegmentMethod(this)(t,this,e,i)}path(t,e,i){const s=this.segments,a=_getSegmentMethod(this);let n=this._loop;e=e||0,i=i||this.points.length-e;for(const o of s)n&=a(t,this,o,{start:e,end:e+i-1});return!!n}draw(t,e,i,s){const a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),draw(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function inRange$1(t,e,i,s){const a=t.options,{[i]:n}=t.getProps([i],s);return Math.abs(e-n)<a.radius+a.hitRadius}class PointElement extends Element{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){const s=this.options,{x:a,y:n}=this.getProps(["x","y"],i);return Math.pow(t-a,2)+Math.pow(e-n,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return inRange$1(this,t,"x",e)}inYRange(t,e){return inRange$1(this,t,"y",e)}getCenterPoint(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0;e=Math.max(e,e&&t.hoverRadius||0);return 2*(e+(e&&t.borderWidth||0))}draw(t,e){const i=this.options;this.skip||i.radius<.1||!_isPointInArea(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,drawPoint(t,i,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function getBarBounds(t,e){const{x:i,y:s,base:a,width:n,height:o}=t.getProps(["x","y","base","width","height"],e);let r,l,h,c,d;return t.horizontal?(d=o/2,r=Math.min(i,a),l=Math.max(i,a),h=s-d,c=s+d):(d=n/2,r=i-d,l=i+d,h=Math.min(s,a),c=Math.max(s,a)),{left:r,top:h,right:l,bottom:c}}function skipOrLimit(t,e,i,s){return t?0:_limitValue(e,i,s)}function parseBorderWidth(t,e,i){const s=t.options.borderWidth,a=t.borderSkipped,n=toTRBL(s);return{t:skipOrLimit(a.top,n.top,0,i),r:skipOrLimit(a.right,n.right,0,e),b:skipOrLimit(a.bottom,n.bottom,0,i),l:skipOrLimit(a.left,n.left,0,e)}}function parseBorderRadius(t,e,i){const{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),a=t.options.borderRadius,n=toTRBLCorners(a),o=Math.min(e,i),r=t.borderSkipped,l=s||isObject(a);return{topLeft:skipOrLimit(!l||r.top||r.left,n.topLeft,0,o),topRight:skipOrLimit(!l||r.top||r.right,n.topRight,0,o),bottomLeft:skipOrLimit(!l||r.bottom||r.left,n.bottomLeft,0,o),bottomRight:skipOrLimit(!l||r.bottom||r.right,n.bottomRight,0,o)}}function boundingRects(t){const e=getBarBounds(t),i=e.right-e.left,s=e.bottom-e.top,a=parseBorderWidth(t,i/2,s/2),n=parseBorderRadius(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:n},inner:{x:e.left+a.l,y:e.top+a.t,w:i-a.l-a.r,h:s-a.t-a.b,radius:{topLeft:Math.max(0,n.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,n.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,n.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,n.bottomRight-Math.max(a.b,a.r))}}}}function inRange(t,e,i,s){const a=null===e,n=null===i,o=t&&!(a&&n)&&getBarBounds(t,s);return o&&(a||_isBetween(e,o.left,o.right))&&(n||_isBetween(i,o.top,o.bottom))}function hasRadius(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function addNormalRectPath(t,e){t.rect(e.x,e.y,e.w,e.h)}function inflateRect(t,e,i={}){const s=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,n=(t.x+t.w!==i.x+i.w?e:0)-s,o=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+s,y:t.y+a,w:t.w+n,h:t.h+o,radius:t.radius}}class BarElement extends Element{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:i,backgroundColor:s}}=this,{inner:a,outer:n}=boundingRects(this),o=hasRadius(n.radius)?addRoundedRectPath:addNormalRectPath;t.save(),n.w===a.w&&n.h===a.h||(t.beginPath(),o(t,inflateRect(n,e,a)),t.clip(),o(t,inflateRect(a,-e,n)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),o(t,inflateRect(a,e)),t.fillStyle=s,t.fill(),t.restore()}inRange(t,e,i){return inRange(this,t,e,i)}inXRange(t,e){return inRange(this,t,null,e)}inYRange(t,e){return inRange(this,null,t,e)}getCenterPoint(t){const{x:e,y:i,base:s,horizontal:a}=this.getProps(["x","y","base","horizontal"],t);return{x:a?(e+s)/2:e,y:a?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}var elements=Object.freeze({__proto__:null,ArcElement:ArcElement,BarElement:BarElement,LineElement:LineElement,PointElement:PointElement});const BORDER_COLORS=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],BACKGROUND_COLORS=BORDER_COLORS.map((t=>t.replace("rgb(","rgba(").replace(")",", 0.5)")));function getBorderColor(t){return BORDER_COLORS[t%BORDER_COLORS.length]}function getBackgroundColor(t){return BACKGROUND_COLORS[t%BACKGROUND_COLORS.length]}function colorizeDefaultDataset(t,e){return t.borderColor=getBorderColor(e),t.backgroundColor=getBackgroundColor(e),++e}function colorizeDoughnutDataset(t,e){return t.backgroundColor=t.data.map((()=>getBorderColor(e++))),e}function colorizePolarAreaDataset(t,e){return t.backgroundColor=t.data.map((()=>getBackgroundColor(e++))),e}function getColorizer(t){let e=0;return(i,s)=>{const a=t.getDatasetMeta(s).controller;a instanceof DoughnutController?e=colorizeDoughnutDataset(i,e):a instanceof PolarAreaController?e=colorizePolarAreaDataset(i,e):a&&(e=colorizeDefaultDataset(i,e))}}function containsColorsDefinitions(t){let e;for(e in t)if(t[e].borderColor||t[e].backgroundColor)return!0;return!1}function containsColorsDefinition(t){return t&&(t.borderColor||t.backgroundColor)}function containsDefaultColorsDefenitions(){return"rgba(0,0,0,0.1)"!==defaults.borderColor||"rgba(0,0,0,0.1)"!==defaults.backgroundColor}var plugin_colors={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(t,e,i){if(!i.enabled)return;const{data:{datasets:s},options:a}=t.config,{elements:n}=a,o=containsColorsDefinitions(s)||containsColorsDefinition(a)||n&&containsColorsDefinitions(n)||containsDefaultColorsDefenitions();if(!i.forceOverride&&o)return;const r=getColorizer(t);s.forEach(r)}};function lttbDecimation(t,e,i,s,a){const n=a.samples||s;if(n>=i)return t.slice(e,e+i);const o=[],r=(i-2)/(n-2);let l=0;const h=e+i-1;let c,d,u,g,p,f=e;for(o[l++]=t[f],c=0;c<n-2;c++){let s,a=0,n=0;const h=Math.floor((c+1)*r)+1+e,m=Math.min(Math.floor((c+2)*r)+1,i)+e,x=m-h;for(s=h;s<m;s++)a+=t[s].x,n+=t[s].y;a/=x,n/=x;const b=Math.floor(c*r)+1+e,_=Math.min(Math.floor((c+1)*r)+1,i)+e,{x:y,y:v}=t[f];for(u=g=-1,s=b;s<_;s++)g=.5*Math.abs((y-a)*(t[s].y-v)-(y-t[s].x)*(n-v)),g>u&&(u=g,d=t[s],p=s);o[l++]=d,f=p}return o[l++]=t[h],o}function minMaxDecimation(t,e,i,s){let a,n,o,r,l,h,c,d,u,g,p=0,f=0;const m=[],x=e+i-1,b=t[e].x,_=t[x].x-b;for(a=e;a<e+i;++a){n=t[a],o=(n.x-b)/_*s,r=n.y;const e=0|o;if(e===l)r<u?(u=r,h=a):r>g&&(g=r,c=a),p=(f*p+n.x)/++f;else{const i=a-1;if(!isNullOrUndef(h)&&!isNullOrUndef(c)){const e=Math.min(h,c),s=Math.max(h,c);e!==d&&e!==i&&m.push({...t[e],x:p}),s!==d&&s!==i&&m.push({...t[s],x:p})}a>0&&i!==d&&m.push(t[i]),m.push(n),l=e,f=0,u=g=r,h=c=d=a}}return m}function cleanDecimatedDataset(t){if(t._decimated){const e=t._data;delete t._decimated,delete t._data,Object.defineProperty(t,"data",{configurable:!0,enumerable:!0,writable:!0,value:e})}}function cleanDecimatedData(t){t.data.datasets.forEach((t=>{cleanDecimatedDataset(t)}))}function getStartAndCountOfVisiblePointsSimplified(t,e){const i=e.length;let s,a=0;const{iScale:n}=t,{min:o,max:r,minDefined:l,maxDefined:h}=n.getUserBounds();return l&&(a=_limitValue(_lookupByKey(e,n.axis,o).lo,0,i-1)),s=h?_limitValue(_lookupByKey(e,n.axis,r).hi+1,a,i)-a:i-a,{start:a,count:s}}var plugin_decimation={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(t,e,i)=>{if(!i.enabled)return void cleanDecimatedData(t);const s=t.width;t.data.datasets.forEach(((e,a)=>{const{_data:n,indexAxis:o}=e,r=t.getDatasetMeta(a),l=n||e.data;if("y"===resolve([o,t.options.indexAxis]))return;if(!r.controller.supportsDecimation)return;const h=t.scales[r.xAxisID];if("linear"!==h.type&&"time"!==h.type)return;if(t.options.parsing)return;let{start:c,count:d}=getStartAndCountOfVisiblePointsSimplified(r,l);if(d<=(i.threshold||4*s))return void cleanDecimatedDataset(e);let u;switch(isNullOrUndef(n)&&(e._data=l,delete e.data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(t){this._data=t}})),i.algorithm){case"lttb":u=lttbDecimation(l,c,d,s,i);break;case"min-max":u=minMaxDecimation(l,c,d,s);break;default:throw new Error(`Unsupported decimation algorithm '${i.algorithm}'`)}e._decimated=u}))},destroy(t){cleanDecimatedData(t)}};function _segments(t,e,i){const s=t.segments,a=t.points,n=e.points,o=[];for(const t of s){let{start:s,end:r}=t;r=_findSegmentEnd(s,r,a);const l=_getBounds(i,a[s],a[r],t.loop);if(!e.segments){o.push({source:t,target:l,start:a[s],end:a[r]});continue}const h=_boundSegments(e,l);for(const e of h){const s=_getBounds(i,n[e.start],n[e.end],e.loop),r=_boundSegment(t,a,s);for(const t of r)o.push({source:t,target:e,start:{[i]:_getEdge(l,s,"start",Math.max)},end:{[i]:_getEdge(l,s,"end",Math.min)}})}}return o}function _getBounds(t,e,i,s){if(s)return;let a=e[t],n=i[t];return"angle"===t&&(a=_normalizeAngle(a),n=_normalizeAngle(n)),{property:t,start:a,end:n}}function _pointsFromSegments(t,e){const{x:i=null,y:s=null}=t||{},a=e.points,n=[];return e.segments.forEach((({start:t,end:e})=>{e=_findSegmentEnd(t,e,a);const o=a[t],r=a[e];null!==s?(n.push({x:o.x,y:s}),n.push({x:r.x,y:s})):null!==i&&(n.push({x:i,y:o.y}),n.push({x:i,y:r.y}))})),n}function _findSegmentEnd(t,e,i){for(;e>t;e--){const t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function _getEdge(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function _createBoundaryLine(t,e){let i=[],s=!1;return isArray(t)?(s=!0,i=t):i=_pointsFromSegments(t,e),i.length?new LineElement({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}function _shouldApplyFill(t){return t&&!1!==t.fill}function _resolveTarget(t,e,i){let s=t[e].fill;const a=[e];let n;if(!i)return s;for(;!1!==s&&-1===a.indexOf(s);){if(!isNumberFinite(s))return s;if(n=t[s],!n)return!1;if(n.visible)return s;a.push(s),s=n.fill}return!1}function _decodeFill(t,e,i){const s=parseFillOption(t);if(isObject(s))return!isNaN(s.value)&&s;let a=parseFloat(s);return isNumberFinite(a)&&Math.floor(a)===a?decodeTargetIndex(s[0],e,a,i):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function decodeTargetIndex(t,e,i,s){return"-"!==t&&"+"!==t||(i=e+i),!(i===e||i<0||i>=s)&&i}function _getTargetPixel(t,e){let i=null;return"start"===t?i=e.bottom:"end"===t?i=e.top:isObject(t)?i=e.getPixelForValue(t.value):e.getBasePixel&&(i=e.getBasePixel()),i}function _getTargetValue(t,e,i){let s;return s="start"===t?i:"end"===t?e.options.reverse?e.min:e.max:isObject(t)?t.value:e.getBaseValue(),s}function parseFillOption(t){const e=t.options,i=e.fill;let s=valueOrDefault(i&&i.target,i);return void 0===s&&(s=!!e.backgroundColor),!1!==s&&null!==s&&(!0===s?"origin":s)}function _buildStackLine(t){const{scale:e,index:i,line:s}=t,a=[],n=s.segments,o=s.points,r=getLinesBelow(e,i);r.push(_createBoundaryLine({x:null,y:e.bottom},s));for(let t=0;t<n.length;t++){const e=n[t];for(let t=e.start;t<=e.end;t++)addPointsBelow(a,o[t],r)}return new LineElement({points:a,options:{}})}function getLinesBelow(t,e){const i=[],s=t.getMatchingVisibleMetas("line");for(let t=0;t<s.length;t++){const a=s[t];if(a.index===e)break;a.hidden||i.unshift(a.dataset)}return i}function addPointsBelow(t,e,i){const s=[];for(let a=0;a<i.length;a++){const n=i[a],{first:o,last:r,point:l}=findPoint(n,e,"x");if(!(!l||o&&r))if(o)s.unshift(l);else if(t.push(l),!r)break}t.push(...s)}function findPoint(t,e,i){const s=t.interpolate(e,i);if(!s)return{};const a=s[i],n=t.segments,o=t.points;let r=!1,l=!1;for(let t=0;t<n.length;t++){const e=n[t],s=o[e.start][i],h=o[e.end][i];if(_isBetween(a,s,h)){r=a===s,l=a===h;break}}return{first:r,last:l,point:s}}class simpleArc{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:s,y:a,radius:n}=this;return e=e||{start:0,end:TAU},t.arc(s,a,n,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:s}=this,a=t.angle;return{x:e+Math.cos(a)*s,y:i+Math.sin(a)*s,angle:a}}}function _getTarget(t){const{chart:e,fill:i,line:s}=t;if(isNumberFinite(i))return getLineByIndex(e,i);if("stack"===i)return _buildStackLine(t);if("shape"===i)return!0;const a=computeBoundary(t);return a instanceof simpleArc?a:_createBoundaryLine(a,s)}function getLineByIndex(t,e){const i=t.getDatasetMeta(e);return i&&t.isDatasetVisible(e)?i.dataset:null}function computeBoundary(t){return(t.scale||{}).getPointPositionForValue?computeCircularBoundary(t):computeLinearBoundary(t)}function computeLinearBoundary(t){const{scale:e={},fill:i}=t,s=_getTargetPixel(i,e);if(isNumberFinite(s)){const t=e.isHorizontal();return{x:t?s:null,y:t?null:s}}return null}function computeCircularBoundary(t){const{scale:e,fill:i}=t,s=e.options,a=e.getLabels().length,n=s.reverse?e.max:e.min,o=_getTargetValue(i,e,n),r=[];if(s.grid.circular){const t=e.getPointPositionForValue(0,n);return new simpleArc({x:t.x,y:t.y,radius:e.getDistanceFromCenterForValue(o)})}for(let t=0;t<a;++t)r.push(e.getPointPositionForValue(t,o));return r}function _drawfill(t,e,i){const s=_getTarget(e),{chart:a,index:n,line:o,scale:r,axis:l}=e,h=o.options,c=h.fill,d=h.backgroundColor,{above:u=d,below:g=d}=c||{},p=a.getDatasetMeta(n),f=getDatasetClipArea(a,p);s&&o.points.length&&(clipArea(t,i),doFill(t,{line:o,target:s,above:u,below:g,area:i,scale:r,axis:l,clip:f}),unclipArea(t))}function doFill(t,e){const{line:i,target:s,above:a,below:n,area:o,scale:r,clip:l}=e,h=i._loop?"angle":e.axis;t.save();let c=n;n!==a&&("x"===h?(clipVertical(t,s,o.top),fill(t,{line:i,target:s,color:a,scale:r,property:h,clip:l}),t.restore(),t.save(),clipVertical(t,s,o.bottom)):"y"===h&&(clipHorizontal(t,s,o.left),fill(t,{line:i,target:s,color:n,scale:r,property:h,clip:l}),t.restore(),t.save(),clipHorizontal(t,s,o.right),c=a)),fill(t,{line:i,target:s,color:c,scale:r,property:h,clip:l}),t.restore()}function clipVertical(t,e,i){const{segments:s,points:a}=e;let n=!0,o=!1;t.beginPath();for(const r of s){const{start:s,end:l}=r,h=a[s],c=a[_findSegmentEnd(s,l,a)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),o=!!e.pathSegment(t,r,{move:o}),o?t.closePath():t.lineTo(c.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function clipHorizontal(t,e,i){const{segments:s,points:a}=e;let n=!0,o=!1;t.beginPath();for(const r of s){const{start:s,end:l}=r,h=a[s],c=a[_findSegmentEnd(s,l,a)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(i,h.y),t.lineTo(h.x,h.y)),o=!!e.pathSegment(t,r,{move:o}),o?t.closePath():t.lineTo(i,c.y)}t.lineTo(i,e.first().y),t.closePath(),t.clip()}function fill(t,e){const{line:i,target:s,property:a,color:n,scale:o,clip:r}=e,l=_segments(i,s,a);for(const{source:e,target:h,start:c,end:d}of l){const{style:{backgroundColor:l=n}={}}=e,u=!0!==s;t.save(),t.fillStyle=l,clipBounds(t,o,r,u&&_getBounds(a,c,d)),t.beginPath();const g=!!i.pathSegment(t,e);let p;if(u){g?t.closePath():interpolatedLineTo(t,s,d,a);const e=!!s.pathSegment(t,h,{move:g,reverse:!0});p=g&&e,p||interpolatedLineTo(t,s,c,a)}t.closePath(),t.fill(p?"evenodd":"nonzero"),t.restore()}}function clipBounds(t,e,i,s){const a=e.chart.chartArea,{property:n,start:o,end:r}=s||{};if("x"===n||"y"===n){let e,s,l,h;"x"===n?(e=o,s=a.top,l=r,h=a.bottom):(e=a.left,s=o,l=a.right,h=r),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),s=Math.max(s,i.top),h=Math.min(h,i.bottom)),t.rect(e,s,l-e,h-s),t.clip()}}function interpolatedLineTo(t,e,i,s){const a=e.interpolate(i,s);a&&t.lineTo(a.x,a.y)}var index={id:"filler",afterDatasetsUpdate(t,e,i){const s=(t.data.datasets||[]).length,a=[];let n,o,r,l;for(o=0;o<s;++o)n=t.getDatasetMeta(o),r=n.dataset,l=null,r&&r.options&&r instanceof LineElement&&(l={visible:t.isDatasetVisible(o),index:o,fill:_decodeFill(r,o,s),chart:t,axis:n.controller.options.indexAxis,scale:n.vScale,line:r}),n.$filler=l,a.push(l);for(o=0;o<s;++o)l=a[o],l&&!1!==l.fill&&(l.fill=_resolveTarget(a,o,i.propagate))},beforeDraw(t,e,i){const s="beforeDraw"===i.drawTime,a=t.getSortedVisibleDatasetMetas(),n=t.chartArea;for(let e=a.length-1;e>=0;--e){const i=a[e].$filler;i&&(i.line.updateControlPoints(n,i.axis),s&&i.fill&&_drawfill(t.ctx,i,n))}},beforeDatasetsDraw(t,e,i){if("beforeDatasetsDraw"!==i.drawTime)return;const s=t.getSortedVisibleDatasetMetas();for(let e=s.length-1;e>=0;--e){const i=s[e].$filler;_shouldApplyFill(i)&&_drawfill(t.ctx,i,t.chartArea)}},beforeDatasetDraw(t,e,i){const s=e.meta.$filler;_shouldApplyFill(s)&&"beforeDatasetDraw"===i.drawTime&&_drawfill(t.ctx,s,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const getBoxSize=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},itemsEqual=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Legend extends Element{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=callback(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter((e=>t.filter(e,this.chart.data)))),t.sort&&(e=e.sort(((e,i)=>t.sort(e,i,this.chart.data)))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display)return void(this.width=this.height=0);const i=t.labels,s=toFont(i.font),a=s.size,n=this._computeTitleHeight(),{boxWidth:o,itemHeight:r}=getBoxSize(i,a);let l,h;e.font=s.string,this.isHorizontal()?(l=this.maxWidth,h=this._fitRows(n,a,o,r)+10):(h=this.maxHeight,l=this._fitCols(n,s,o,r)+10),this.width=Math.min(l,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){const{ctx:a,maxWidth:n,options:{labels:{padding:o}}}=this,r=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+o;let c=t;a.textAlign="left",a.textBaseline="middle";let d=-1,u=-h;return this.legendItems.forEach(((t,g)=>{const p=i+e/2+a.measureText(t.text).width;(0===g||l[l.length-1]+p+2*o>n)&&(c+=h,l[l.length-(g>0?0:1)]=0,u+=h,d++),r[g]={left:0,top:u,row:d,width:p,height:s},l[l.length-1]+=p+o})),c}_fitCols(t,e,i,s){const{ctx:a,maxHeight:n,options:{labels:{padding:o}}}=this,r=this.legendHitBoxes=[],l=this.columnSizes=[],h=n-t;let c=o,d=0,u=0,g=0,p=0;return this.legendItems.forEach(((t,n)=>{const{itemWidth:f,itemHeight:m}=calculateItemSize(i,e,a,t,s);n>0&&u+m+2*o>h&&(c+=d+o,l.push({width:d,height:u}),g+=d+o,p++,d=u=0),r[n]={left:g,top:u,col:p,width:f,height:m},d=Math.max(d,f),u+=m+o})),c+=d,l.push({width:d,height:u}),c}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:a}}=this,n=getRtlAdapter(a,this.left,this.width);if(this.isHorizontal()){let a=0,o=_alignStartEnd(i,this.left+s,this.right-this.lineWidths[a]);for(const r of e)a!==r.row&&(a=r.row,o=_alignStartEnd(i,this.left+s,this.right-this.lineWidths[a])),r.top+=this.top+t+s,r.left=n.leftForLtr(n.x(o),r.width),o+=r.width+s}else{let a=0,o=_alignStartEnd(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(const r of e)r.col!==a&&(a=r.col,o=_alignStartEnd(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),r.top=o,r.left+=this.left+s,r.left=n.leftForLtr(n.x(r.left),r.width),o+=r.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const t=this.ctx;clipArea(t,this),this._draw(),unclipArea(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:a,labels:n}=t,o=defaults.color,r=getRtlAdapter(t.rtl,this.left,this.width),l=toFont(n.font),{padding:h}=n,c=l.size,d=c/2;let u;this.drawTitle(),s.textAlign=r.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=l.string;const{boxWidth:g,boxHeight:p,itemHeight:f}=getBoxSize(n,c),m=this.isHorizontal(),x=this._computeTitleHeight();u=m?{x:_alignStartEnd(a,this.left+h,this.right-i[0]),y:this.top+h+x,line:0}:{x:this.left+h,y:_alignStartEnd(a,this.top+x+h,this.bottom-e[0].height),line:0},overrideTextDirection(this.ctx,t.textDirection);const b=f+h;this.legendItems.forEach(((_,y)=>{s.strokeStyle=_.fontColor,s.fillStyle=_.fontColor;const v=s.measureText(_.text).width,k=r.textAlign(_.textAlign||(_.textAlign=n.textAlign)),S=g+d+v;let D=u.x,M=u.y;r.setWidth(this.width),m?y>0&&D+S+h>this.right&&(M=u.y+=b,u.line++,D=u.x=_alignStartEnd(a,this.left+h,this.right-i[u.line])):y>0&&M+b>this.bottom&&(D=u.x=D+e[u.line].width+h,u.line++,M=u.y=_alignStartEnd(a,this.top+x+h,this.bottom-e[u.line].height));if(function(t,e,i){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;s.save();const a=valueOrDefault(i.lineWidth,1);if(s.fillStyle=valueOrDefault(i.fillStyle,o),s.lineCap=valueOrDefault(i.lineCap,"butt"),s.lineDashOffset=valueOrDefault(i.lineDashOffset,0),s.lineJoin=valueOrDefault(i.lineJoin,"miter"),s.lineWidth=a,s.strokeStyle=valueOrDefault(i.strokeStyle,o),s.setLineDash(valueOrDefault(i.lineDash,[])),n.usePointStyle){const o={radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:a},l=r.xPlus(t,g/2);drawPointLegend(s,o,l,e+d,n.pointStyleWidth&&g)}else{const n=e+Math.max((c-p)/2,0),o=r.leftForLtr(t,g),l=toTRBLCorners(i.borderRadius);s.beginPath(),Object.values(l).some((t=>0!==t))?addRoundedRectPath(s,{x:o,y:n,w:g,h:p,radius:l}):s.rect(o,n,g,p),s.fill(),0!==a&&s.stroke()}s.restore()}(r.x(D),M,_),D=_textX(k,D+g+d,m?D+S:this.right,t.rtl),function(t,e,i){renderText(s,i.text,t,e+f/2,l,{strikethrough:i.hidden,textAlign:r.textAlign(i.textAlign)})}(r.x(D),M,_),m)u.x+=S+h;else if("string"!=typeof _.text){const t=l.lineHeight;u.y+=calculateLegendItemHeight(_,t)+h}else u.y+=b})),restoreTextDirection(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=toFont(e.font),s=toPadding(e.padding);if(!e.display)return;const a=getRtlAdapter(t.rtl,this.left,this.width),n=this.ctx,o=e.position,r=i.size/2,l=s.top+r;let h,c=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+l,c=_alignStartEnd(t.align,c,this.right-d);else{const e=this.columnSizes.reduce(((t,e)=>Math.max(t,e.height)),0);h=l+_alignStartEnd(t.align,this.top,this.bottom-e-t.labels.padding-this._computeTitleHeight())}const u=_alignStartEnd(o,c,c+d);n.textAlign=a.textAlign(_toLeftRightCenter(o)),n.textBaseline="middle",n.strokeStyle=e.color,n.fillStyle=e.color,n.font=i.string,renderText(n,e.text,u,h,i)}_computeTitleHeight(){const t=this.options.title,e=toFont(t.font),i=toPadding(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,a;if(_isBetween(t,this.left,this.right)&&_isBetween(e,this.top,this.bottom))for(a=this.legendHitBoxes,i=0;i<a.length;++i)if(s=a[i],_isBetween(t,s.left,s.left+s.width)&&_isBetween(e,s.top,s.top+s.height))return this.legendItems[i];return null}handleEvent(t){const e=this.options;if(!isListened(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){const s=this._hoveredItem,a=itemsEqual(s,i);s&&!a&&callback(e.onLeave,[t,s,this],this),this._hoveredItem=i,i&&!a&&callback(e.onHover,[t,i,this],this)}else i&&callback(e.onClick,[t,i,this],this)}}function calculateItemSize(t,e,i,s,a){return{itemWidth:calculateItemWidth(s,t,e,i),itemHeight:calculateItemHeight(a,s,e.lineHeight)}}function calculateItemWidth(t,e,i,s){let a=t.text;return a&&"string"!=typeof a&&(a=a.reduce(((t,e)=>t.length>e.length?t:e))),e+i.size/2+s.measureText(a).width}function calculateItemHeight(t,e,i){let s=t;return"string"!=typeof e.text&&(s=calculateLegendItemHeight(e,i)),s}function calculateLegendItemHeight(t,e){return e*(t.text?t.text.length:0)}function isListened(t,e){return!("mousemove"!==t&&"mouseout"!==t||!e.onHover&&!e.onLeave)||!(!e.onClick||"click"!==t&&"mouseup"!==t)}var plugin_legend={id:"legend",_element:Legend,start(t,e,i){const s=t.legend=new Legend({ctx:t.ctx,options:i,chart:t});layouts.configure(t,s,i),layouts.addBox(t,s)},stop(t){layouts.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){const s=t.legend;layouts.configure(t,s,i),s.options=i},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){const s=e.datasetIndex,a=i.chart;a.isDatasetVisible(s)?(a.hide(s),e.hidden=!0):(a.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:a,color:n,useBorderRadius:o,borderRadius:r}}=t.legend.options;return t._getSortedDatasetMetas().map((t=>{const l=t.controller.getStyle(i?0:void 0),h=toPadding(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:n,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:a||l.textAlign,borderRadius:o&&(r||l.borderRadius),datasetIndex:t.index}}),this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Title extends Element{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=t,this.height=this.bottom=e;const s=isArray(i.text)?i.text.length:1;this._padding=toPadding(i.padding);const a=s*toFont(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=a:this.width=a}isHorizontal(){const t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){const{top:e,left:i,bottom:s,right:a,options:n}=this,o=n.align;let r,l,h,c=0;return this.isHorizontal()?(l=_alignStartEnd(o,i,a),h=e+t,r=a-i):("left"===n.position?(l=i+t,h=_alignStartEnd(o,s,e),c=-.5*PI):(l=a-t,h=_alignStartEnd(o,e,s),c=.5*PI),r=s-e),{titleX:l,titleY:h,maxWidth:r,rotation:c}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=toFont(e.font),s=i.lineHeight/2+this._padding.top,{titleX:a,titleY:n,maxWidth:o,rotation:r}=this._drawArgs(s);renderText(t,e.text,0,0,i,{color:e.color,maxWidth:o,rotation:r,textAlign:_toLeftRightCenter(e.align),textBaseline:"middle",translation:[a,n]})}}function createTitle(t,e){const i=new Title({ctx:t.ctx,options:e,chart:t});layouts.configure(t,i,e),layouts.addBox(t,i),t.titleBlock=i}var plugin_title={id:"title",_element:Title,start(t,e,i){createTitle(t,i)},stop(t){const e=t.titleBlock;layouts.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){const s=t.titleBlock;layouts.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const map=new WeakMap;var plugin_subtitle={id:"subtitle",start(t,e,i){const s=new Title({ctx:t.ctx,options:i,chart:t});layouts.configure(t,s,i),layouts.addBox(t,s),map.set(t,s)},stop(t){layouts.removeBox(t,map.get(t)),map.delete(t)},beforeUpdate(t,e,i){const s=map.get(t);layouts.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const positioners={average(t){if(!t.length)return!1;let e,i,s=new Set,a=0,n=0;for(e=0,i=t.length;e<i;++e){const i=t[e].element;if(i&&i.hasValue()){const t=i.tooltipPosition();s.add(t.x),a+=t.y,++n}}if(0===n||0===s.size)return!1;return{x:[...s].reduce(((t,e)=>t+e))/s.size,y:a/n}},nearest(t,e){if(!t.length)return!1;let i,s,a,n=e.x,o=e.y,r=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){const s=t[i].element;if(s&&s.hasValue()){const t=s.getCenterPoint(),i=distanceBetweenPoints(e,t);i<r&&(r=i,a=s)}}if(a){const t=a.tooltipPosition();n=t.x,o=t.y}return{x:n,y:o}}};function pushOrConcat(t,e){return e&&(isArray(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function splitNewlines(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function createTooltipItem(t,e){const{element:i,datasetIndex:s,index:a}=e,n=t.getDatasetMeta(s).controller,{label:o,value:r}=n.getLabelAndValue(a);return{chart:t,label:o,parsed:n.getParsed(a),raw:t.data.datasets[s].data[a],formattedValue:r,dataset:n.getDataset(),dataIndex:a,datasetIndex:s,element:i}}function getTooltipSize(t,e){const i=t.chart.ctx,{body:s,footer:a,title:n}=t,{boxWidth:o,boxHeight:r}=e,l=toFont(e.bodyFont),h=toFont(e.titleFont),c=toFont(e.footerFont),d=n.length,u=a.length,g=s.length,p=toPadding(e.padding);let f=p.height,m=0,x=s.reduce(((t,e)=>t+e.before.length+e.lines.length+e.after.length),0);if(x+=t.beforeBody.length+t.afterBody.length,d&&(f+=d*h.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),x){f+=g*(e.displayColors?Math.max(r,l.lineHeight):l.lineHeight)+(x-g)*l.lineHeight+(x-1)*e.bodySpacing}u&&(f+=e.footerMarginTop+u*c.lineHeight+(u-1)*e.footerSpacing);let b=0;const _=function(t){m=Math.max(m,i.measureText(t).width+b)};return i.save(),i.font=h.string,each(t.title,_),i.font=l.string,each(t.beforeBody.concat(t.afterBody),_),b=e.displayColors?o+2+e.boxPadding:0,each(s,(t=>{each(t.before,_),each(t.lines,_),each(t.after,_)})),b=0,i.font=c.string,each(t.footer,_),i.restore(),m+=p.width,{width:m,height:f}}function determineYAlign(t,e){const{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}function doesNotFitWithAlign(t,e,i,s){const{x:a,width:n}=s,o=i.caretSize+i.caretPadding;return"left"===t&&a+n+o>e.width||("right"===t&&a-n-o<0||void 0)}function determineXAlign(t,e,i,s){const{x:a,width:n}=i,{width:o,chartArea:{left:r,right:l}}=t;let h="center";return"center"===s?h=a<=(r+l)/2?"left":"right":a<=n/2?h="left":a>=o-n/2&&(h="right"),doesNotFitWithAlign(h,t,e,i)&&(h="center"),h}function determineAlignment(t,e,i){const s=i.yAlign||e.yAlign||determineYAlign(t,i);return{xAlign:i.xAlign||e.xAlign||determineXAlign(t,e,i,s),yAlign:s}}function alignX(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}function alignY(t,e,i){let{y:s,height:a}=t;return"top"===e?s+=i:s-="bottom"===e?a+i:a/2,s}function getBackgroundPoint(t,e,i,s){const{caretSize:a,caretPadding:n,cornerRadius:o}=t,{xAlign:r,yAlign:l}=i,h=a+n,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:g}=toTRBLCorners(o);let p=alignX(e,r);const f=alignY(e,l,h);return"center"===l?"left"===r?p+=h:"right"===r&&(p-=h):"left"===r?p-=Math.max(c,u)+a:"right"===r&&(p+=Math.max(d,g)+a),{x:_limitValue(p,0,s.width-e.width),y:_limitValue(f,0,s.height-e.height)}}function getAlignedX(t,e,i){const s=toPadding(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function getBeforeAfterBodyLines(t){return pushOrConcat([],splitNewlines(t))}function createTooltipContext(t,e,i){return createContext(t,{tooltip:e,tooltipItems:i,type:"tooltip"})}function overrideCallbacks(t,e){const i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}const defaultCallbacks={beforeTitle:noop,title(t){if(t.length>0){const e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:noop,beforeBody:noop,beforeLabel:noop,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const i=t.formattedValue;return isNullOrUndef(i)||(e+=i),e},labelColor(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:noop,afterBody:noop,beforeFooter:noop,footer:noop,afterFooter:noop};function invokeCallbackWithFallback(t,e,i,s){const a=t[e].call(i,s);return void 0===a?defaultCallbacks[e].call(i,s):a}class Tooltip extends Element{static positioners=positioners;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,a=new Animations(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=createTooltipContext(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:i}=e,s=invokeCallbackWithFallback(i,"beforeTitle",this,t),a=invokeCallbackWithFallback(i,"title",this,t),n=invokeCallbackWithFallback(i,"afterTitle",this,t);let o=[];return o=pushOrConcat(o,splitNewlines(s)),o=pushOrConcat(o,splitNewlines(a)),o=pushOrConcat(o,splitNewlines(n)),o}getBeforeBody(t,e){return getBeforeAfterBodyLines(invokeCallbackWithFallback(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,s=[];return each(t,(t=>{const e={before:[],lines:[],after:[]},a=overrideCallbacks(i,t);pushOrConcat(e.before,splitNewlines(invokeCallbackWithFallback(a,"beforeLabel",this,t))),pushOrConcat(e.lines,invokeCallbackWithFallback(a,"label",this,t)),pushOrConcat(e.after,splitNewlines(invokeCallbackWithFallback(a,"afterLabel",this,t))),s.push(e)})),s}getAfterBody(t,e){return getBeforeAfterBodyLines(invokeCallbackWithFallback(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,s=invokeCallbackWithFallback(i,"beforeFooter",this,t),a=invokeCallbackWithFallback(i,"footer",this,t),n=invokeCallbackWithFallback(i,"afterFooter",this,t);let o=[];return o=pushOrConcat(o,splitNewlines(s)),o=pushOrConcat(o,splitNewlines(a)),o=pushOrConcat(o,splitNewlines(n)),o}_createItems(t){const e=this._active,i=this.chart.data,s=[],a=[],n=[];let o,r,l=[];for(o=0,r=e.length;o<r;++o)l.push(createTooltipItem(this.chart,e[o]));return t.filter&&(l=l.filter(((e,s,a)=>t.filter(e,s,a,i)))),t.itemSort&&(l=l.sort(((e,s)=>t.itemSort(e,s,i)))),each(l,(e=>{const i=overrideCallbacks(t.callbacks,e);s.push(invokeCallbackWithFallback(i,"labelColor",this,e)),a.push(invokeCallbackWithFallback(i,"labelPointStyle",this,e)),n.push(invokeCallbackWithFallback(i,"labelTextColor",this,e))})),this.labelColors=s,this.labelPointStyles=a,this.labelTextColors=n,this.dataPoints=l,l}update(t,e){const i=this.options.setContext(this.getContext()),s=this._active;let a,n=[];if(s.length){const t=positioners[i.position].call(this,s,this._eventPosition);n=this._createItems(i),this.title=this.getTitle(n,i),this.beforeBody=this.getBeforeBody(n,i),this.body=this.getBody(n,i),this.afterBody=this.getAfterBody(n,i),this.footer=this.getFooter(n,i);const e=this._size=getTooltipSize(this,i),o=Object.assign({},t,e),r=determineAlignment(this.chart,i,o),l=getBackgroundPoint(i,o,r,this.chart);this.xAlign=r.xAlign,this.yAlign=r.yAlign,a={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(a={opacity:0});this._tooltipItems=n,this.$context=void 0,a&&this._resolveAnimations().update(this,a),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){const a=this.getCaretPosition(t,i,s);e.lineTo(a.x1,a.y1),e.lineTo(a.x2,a.y2),e.lineTo(a.x3,a.y3)}getCaretPosition(t,e,i){const{xAlign:s,yAlign:a}=this,{caretSize:n,cornerRadius:o}=i,{topLeft:r,topRight:l,bottomLeft:h,bottomRight:c}=toTRBLCorners(o),{x:d,y:u}=t,{width:g,height:p}=e;let f,m,x,b,_,y;return"center"===a?(_=u+p/2,"left"===s?(f=d,m=f-n,b=_+n,y=_-n):(f=d+g,m=f+n,b=_-n,y=_+n),x=f):(m="left"===s?d+Math.max(r,h)+n:"right"===s?d+g-Math.max(l,c)-n:this.caretX,"top"===a?(b=u,_=b-n,f=m-n,x=m+n):(b=u+p,_=b+n,f=m+n,x=m-n),y=b),{x1:f,x2:m,x3:x,y1:b,y2:_,y3:y}}drawTitle(t,e,i){const s=this.title,a=s.length;let n,o,r;if(a){const l=getRtlAdapter(i.rtl,this.x,this.width);for(t.x=getAlignedX(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",n=toFont(i.titleFont),o=i.titleSpacing,e.fillStyle=i.titleColor,e.font=n.string,r=0;r<a;++r)e.fillText(s[r],l.x(t.x),t.y+n.lineHeight/2),t.y+=n.lineHeight+o,r+1===a&&(t.y+=i.titleMarginBottom-o)}}_drawColorBox(t,e,i,s,a){const n=this.labelColors[i],o=this.labelPointStyles[i],{boxHeight:r,boxWidth:l}=a,h=toFont(a.bodyFont),c=getAlignedX(this,"left",a),d=s.x(c),u=r<h.lineHeight?(h.lineHeight-r)/2:0,g=e.y+u;if(a.usePointStyle){const e={radius:Math.min(l,r)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},i=s.leftForLtr(d,l)+l/2,h=g+r/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,drawPoint(t,e,i,h),t.strokeStyle=n.borderColor,t.fillStyle=n.backgroundColor,drawPoint(t,e,i,h)}else{t.lineWidth=isObject(n.borderWidth)?Math.max(...Object.values(n.borderWidth)):n.borderWidth||1,t.strokeStyle=n.borderColor,t.setLineDash(n.borderDash||[]),t.lineDashOffset=n.borderDashOffset||0;const e=s.leftForLtr(d,l),i=s.leftForLtr(s.xPlus(d,1),l-2),o=toTRBLCorners(n.borderRadius);Object.values(o).some((t=>0!==t))?(t.beginPath(),t.fillStyle=a.multiKeyBackground,addRoundedRectPath(t,{x:e,y:g,w:l,h:r,radius:o}),t.fill(),t.stroke(),t.fillStyle=n.backgroundColor,t.beginPath(),addRoundedRectPath(t,{x:i,y:g+1,w:l-2,h:r-2,radius:o}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(e,g,l,r),t.strokeRect(e,g,l,r),t.fillStyle=n.backgroundColor,t.fillRect(i,g+1,l-2,r-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:s}=this,{bodySpacing:a,bodyAlign:n,displayColors:o,boxHeight:r,boxWidth:l,boxPadding:h}=i,c=toFont(i.bodyFont);let d=c.lineHeight,u=0;const g=getRtlAdapter(i.rtl,this.x,this.width),p=function(i){e.fillText(i,g.x(t.x+u),t.y+d/2),t.y+=d+a},f=g.textAlign(n);let m,x,b,_,y,v,k;for(e.textAlign=n,e.textBaseline="middle",e.font=c.string,t.x=getAlignedX(this,f,i),e.fillStyle=i.bodyColor,each(this.beforeBody,p),u=o&&"right"!==f?"center"===n?l/2+h:l+2+h:0,_=0,v=s.length;_<v;++_){for(m=s[_],x=this.labelTextColors[_],e.fillStyle=x,each(m.before,p),b=m.lines,o&&b.length&&(this._drawColorBox(e,t,_,g,i),d=Math.max(c.lineHeight,r)),y=0,k=b.length;y<k;++y)p(b[y]),d=c.lineHeight;each(m.after,p)}u=0,d=c.lineHeight,each(this.afterBody,p),t.y-=a}drawFooter(t,e,i){const s=this.footer,a=s.length;let n,o;if(a){const r=getRtlAdapter(i.rtl,this.x,this.width);for(t.x=getAlignedX(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=r.textAlign(i.footerAlign),e.textBaseline="middle",n=toFont(i.footerFont),e.fillStyle=i.footerColor,e.font=n.string,o=0;o<a;++o)e.fillText(s[o],r.x(t.x),t.y+n.lineHeight/2),t.y+=n.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){const{xAlign:a,yAlign:n}=this,{x:o,y:r}=t,{width:l,height:h}=i,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:g}=toTRBLCorners(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(o+c,r),"top"===n&&this.drawCaret(t,e,i,s),e.lineTo(o+l-d,r),e.quadraticCurveTo(o+l,r,o+l,r+d),"center"===n&&"right"===a&&this.drawCaret(t,e,i,s),e.lineTo(o+l,r+h-g),e.quadraticCurveTo(o+l,r+h,o+l-g,r+h),"bottom"===n&&this.drawCaret(t,e,i,s),e.lineTo(o+u,r+h),e.quadraticCurveTo(o,r+h,o,r+h-u),"center"===n&&"left"===a&&this.drawCaret(t,e,i,s),e.lineTo(o,r+c),e.quadraticCurveTo(o,r,o+c,r),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,s=i&&i.x,a=i&&i.y;if(s||a){const i=positioners[t.position].call(this,this._active,this._eventPosition);if(!i)return;const n=this._size=getTooltipSize(this,t),o=Object.assign({},i,this._size),r=determineAlignment(e,t,o),l=getBackgroundPoint(t,o,r,e);s._to===l.x&&a._to===l.y||(this.xAlign=r.xAlign,this.yAlign=r.yAlign,this.width=n.width,this.height=n.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const s={width:this.width,height:this.height},a={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const n=toPadding(e.padding),o=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&o&&(t.save(),t.globalAlpha=i,this.drawBackground(a,t,s,e),overrideTextDirection(t,e.textDirection),a.y+=n.top,this.drawTitle(a,t,e),this.drawBody(a,t,e),this.drawFooter(a,t,e),restoreTextDirection(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,s=t.map((({datasetIndex:t,index:e})=>{const i=this.chart.getDatasetMeta(t);if(!i)throw new Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}})),a=!_elementsEqual(i,s),n=this._positionChanged(s,e);(a||n)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,a=this._active||[],n=this._getActiveElements(t,a,e,i),o=this._positionChanged(n,t),r=e||!_elementsEqual(n,a)||o;return r&&(this._active=n,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),r}_getActiveElements(t,e,i,s){const a=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter((t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index)));const n=this.chart.getElementsAtEventForMode(t,a.mode,a,i);return a.reverse&&n.reverse(),n}_positionChanged(t,e){const{caretX:i,caretY:s,options:a}=this,n=positioners[a.position].call(this,t,e);return!1!==n&&(i!==n.x||s!==n.y)}}var plugin_tooltip={id:"tooltip",_element:Tooltip,positioners:positioners,afterInit(t,e,i){i&&(t.tooltip=new Tooltip({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){const i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:defaultCallbacks},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},plugins=Object.freeze({__proto__:null,Colors:plugin_colors,Decimation:plugin_decimation,Filler:index,Legend:plugin_legend,SubTitle:plugin_subtitle,Title:plugin_title,Tooltip:plugin_tooltip});const addIfString=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i);function findOrAddLabel(t,e,i,s){const a=t.indexOf(e);if(-1===a)return addIfString(t,e,i,s);return a!==t.lastIndexOf(e)?i:a}const validIndex=(t,e)=>null===t?null:_limitValue(Math.round(t),0,e);function _getLabelForValue(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class CategoryScale extends Scale{static id="category";static defaults={ticks:{callback:_getLabelForValue}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const t=this.getLabels();for(const{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(isNullOrUndef(t))return null;const i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:findOrAddLabel(i,t,valueOrDefault(e,t),this._addedLabels),validIndex(e,i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,s=[];let a=this.getLabels();a=0===t&&e===a.length-1?a:a.slice(t,e+1),this._valueRange=Math.max(a.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return _getLabelForValue.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function generateTicks$1(t,e){const i=[],{bounds:s,step:a,min:n,max:o,precision:r,count:l,maxTicks:h,maxDigits:c,includeBounds:d}=t,u=a||1,g=h-1,{min:p,max:f}=e,m=!isNullOrUndef(n),x=!isNullOrUndef(o),b=!isNullOrUndef(l),_=(f-p)/(c+1);let y,v,k,S,D=niceNum((f-p)/g/u)*u;if(D<1e-14&&!m&&!x)return[{value:p},{value:f}];S=Math.ceil(f/D)-Math.floor(p/D),S>g&&(D=niceNum(S*D/g/u)*u),isNullOrUndef(r)||(y=Math.pow(10,r),D=Math.ceil(D*y)/y),"ticks"===s?(v=Math.floor(p/D)*D,k=Math.ceil(f/D)*D):(v=p,k=f),m&&x&&a&&almostWhole((o-n)/a,D/1e3)?(S=Math.round(Math.min((o-n)/D,h)),D=(o-n)/S,v=n,k=o):b?(v=m?n:v,k=x?o:k,S=l-1,D=(k-v)/S):(S=(k-v)/D,S=almostEquals(S,Math.round(S),D/1e3)?Math.round(S):Math.ceil(S));const M=Math.max(_decimalPlaces(D),_decimalPlaces(v));y=Math.pow(10,isNullOrUndef(r)?M:r),v=Math.round(v*y)/y,k=Math.round(k*y)/y;let P=0;for(m&&(d&&v!==n?(i.push({value:n}),v<n&&P++,almostEquals(Math.round((v+P*D)*y)/y,n,relativeLabelSize(n,_,t))&&P++):v<n&&P++);P<S;++P){const t=Math.round((v+P*D)*y)/y;if(x&&t>o)break;i.push({value:t})}return x&&d&&k!==o?i.length&&almostEquals(i[i.length-1].value,o,relativeLabelSize(o,_,t))?i[i.length-1].value=o:i.push({value:o}):x&&k!==o||i.push({value:k}),i}function relativeLabelSize(t,e,{horizontal:i,minRotation:s}){const a=toRadians(s),n=(i?Math.sin(a):Math.cos(a))||.001,o=.75*e*(""+t).length;return Math.min(e/n,o)}class LinearScaleBase extends Scale{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return isNullOrUndef(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:s,max:a}=this;const n=t=>s=e?s:t,o=t=>a=i?a:t;if(t){const t=sign(s),e=sign(a);t<0&&e<0?o(0):t>0&&e>0&&n(0)}if(s===a){let e=0===a?1:Math.abs(.05*a);o(a+e),t||n(s-e)}this.min=s,this.max=a}getTickLimit(){const t=this.options.ticks;let e,{maxTicksLimit:i,stepSize:s}=t;return s?(e=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,e>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${e} ticks. Limiting to 1000.`),e=1e3)):(e=this.computeTickLimit(),i=i||11),i&&(e=Math.min(i,e)),e}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s=generateTicks$1({maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&_setMinAndMaxByKey(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return formatNumber(t,this.chart.options.locale,this.options.ticks.format)}}class LinearScale extends LinearScaleBase{static id="linear";static defaults={ticks:{callback:Ticks.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=isNumberFinite(t)?t:0,this.max=isNumberFinite(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=toRadians(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,a=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,a.lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const log10Floor=t=>Math.floor(log10(t)),changeExponent=(t,e)=>Math.pow(10,log10Floor(t)+e);function isMajor(t){return 1===t/Math.pow(10,log10Floor(t))}function steps(t,e,i){const s=Math.pow(10,i),a=Math.floor(t/s);return Math.ceil(e/s)-a}function startExp(t,e){let i=log10Floor(e-t);for(;steps(t,e,i)>10;)i++;for(;steps(t,e,i)<10;)i--;return Math.min(i,log10Floor(t))}function generateTicks(t,{min:e,max:i}){e=finiteOrDefault(t.min,e);const s=[],a=log10Floor(e);let n=startExp(e,i),o=n<0?Math.pow(10,Math.abs(n)):1;const r=Math.pow(10,n),l=a>n?Math.pow(10,a):0,h=Math.round((e-l)*o)/o,c=Math.floor((e-l)/r/10)*r*10;let d=Math.floor((h-c)/Math.pow(10,n)),u=finiteOrDefault(t.min,Math.round((l+c+d*Math.pow(10,n))*o)/o);for(;u<i;)s.push({value:u,major:isMajor(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(n++,d=2,o=n>=0?1:o),u=Math.round((l+c+d*Math.pow(10,n))*o)/o;const g=finiteOrDefault(t.max,u);return s.push({value:g,major:isMajor(g),significand:d}),s}class LogarithmicScale extends Scale{static id="logarithmic";static defaults={ticks:{callback:Ticks.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const i=LinearScaleBase.prototype.parse.apply(this,[t,e]);if(0!==i)return isNumberFinite(i)&&i>0?i:null;this._zero=!0}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=isNumberFinite(t)?Math.max(0,t):null,this.max=isNumberFinite(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!isNumberFinite(this._userMin)&&(this.min=t===changeExponent(this.min,0)?changeExponent(this.min,-1):changeExponent(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let i=this.min,s=this.max;const a=e=>i=t?i:e,n=t=>s=e?s:t;i===s&&(i<=0?(a(1),n(10)):(a(changeExponent(i,-1)),n(changeExponent(s,1)))),i<=0&&a(changeExponent(s,-1)),s<=0&&n(changeExponent(i,1)),this.min=i,this.max=s}buildTicks(){const t=this.options,e=generateTicks({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&_setMinAndMaxByKey(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":formatNumber(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=log10(t),this._valueRange=log10(this.max)-log10(t)}getPixelForValue(t){return void 0!==t&&0!==t||(t=this.min),null===t||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(log10(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function getTickBackdropHeight(t){const e=t.ticks;if(e.display&&t.display){const t=toPadding(e.backdropPadding);return valueOrDefault(e.font&&e.font.size,defaults.font.size)+t.height}return 0}function measureLabelSize(t,e,i){return i=isArray(i)?i:[i],{w:_longestText(t,e.string,i),h:i.length*e.lineHeight}}function determineLimits(t,e,i,s,a){return t===s||t===a?{start:e-i/2,end:e+i/2}:t<s||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function fitWithPointLabels(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],a=[],n=t._pointLabels.length,o=t.options.pointLabels,r=o.centerPointLabels?PI/n:0;for(let l=0;l<n;l++){const n=o.setContext(t.getPointLabelContext(l));a[l]=n.padding;const h=t.getPointPosition(l,t.drawingArea+a[l],r),c=toFont(n.font),d=measureLabelSize(t.ctx,c,t._pointLabels[l]);s[l]=d;const u=_normalizeAngle(t.getIndexAngle(l)+r),g=Math.round(toDegrees(u));updateLimits(i,e,u,determineLimits(g,h.x,d.w,0,180),determineLimits(g,h.y,d.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=buildPointLabelItems(t,s,a)}function updateLimits(t,e,i,s,a){const n=Math.abs(Math.sin(i)),o=Math.abs(Math.cos(i));let r=0,l=0;s.start<e.l?(r=(e.l-s.start)/n,t.l=Math.min(t.l,e.l-r)):s.end>e.r&&(r=(s.end-e.r)/n,t.r=Math.max(t.r,e.r+r)),a.start<e.t?(l=(e.t-a.start)/o,t.t=Math.min(t.t,e.t-l)):a.end>e.b&&(l=(a.end-e.b)/o,t.b=Math.max(t.b,e.b+l))}function createPointLabelItem(t,e,i){const s=t.drawingArea,{extra:a,additionalAngle:n,padding:o,size:r}=i,l=t.getPointPosition(e,s+a+o,n),h=Math.round(toDegrees(_normalizeAngle(l.angle+HALF_PI))),c=yForAngle(l.y,r.h,h),d=getTextAlignForAngle(h),u=leftForTextAlign(l.x,r.w,d);return{visible:!0,x:l.x,y:c,textAlign:d,left:u,top:c,right:u+r.w,bottom:c+r.h}}function isNotOverlapped(t,e){if(!e)return!0;const{left:i,top:s,right:a,bottom:n}=t;return!(_isPointInArea({x:i,y:s},e)||_isPointInArea({x:i,y:n},e)||_isPointInArea({x:a,y:s},e)||_isPointInArea({x:a,y:n},e))}function buildPointLabelItems(t,e,i){const s=[],a=t._pointLabels.length,n=t.options,{centerPointLabels:o,display:r}=n.pointLabels,l={extra:getTickBackdropHeight(n)/2,additionalAngle:o?PI/a:0};let h;for(let n=0;n<a;n++){l.padding=i[n],l.size=e[n];const a=createPointLabelItem(t,n,l);s.push(a),"auto"===r&&(a.visible=isNotOverlapped(a,h),a.visible&&(h=a))}return s}function getTextAlignForAngle(t){return 0===t||180===t?"center":t<180?"left":"right"}function leftForTextAlign(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function yForAngle(t,e,i){return 90===i||270===i?t-=e/2:(i>270||i<90)&&(t-=e),t}function drawPointLabelBox(t,e,i){const{left:s,top:a,right:n,bottom:o}=i,{backdropColor:r}=e;if(!isNullOrUndef(r)){const i=toTRBLCorners(e.borderRadius),l=toPadding(e.backdropPadding);t.fillStyle=r;const h=s-l.left,c=a-l.top,d=n-s+l.width,u=o-a+l.height;Object.values(i).some((t=>0!==t))?(t.beginPath(),addRoundedRectPath(t,{x:h,y:c,w:d,h:u,radius:i}),t.fill()):t.fillRect(h,c,d,u)}}function drawPointLabels(t,e){const{ctx:i,options:{pointLabels:s}}=t;for(let a=e-1;a>=0;a--){const e=t._pointLabelItems[a];if(!e.visible)continue;const n=s.setContext(t.getPointLabelContext(a));drawPointLabelBox(i,n,e);const o=toFont(n.font),{x:r,y:l,textAlign:h}=e;renderText(i,t._pointLabels[a],r,l+o.lineHeight/2,o,{color:n.color,textAlign:h,textBaseline:"middle"})}}function pathRadiusLine(t,e,i,s){const{ctx:a}=t;if(i)a.arc(t.xCenter,t.yCenter,e,0,TAU);else{let i=t.getPointPosition(0,e);a.moveTo(i.x,i.y);for(let n=1;n<s;n++)i=t.getPointPosition(n,e),a.lineTo(i.x,i.y)}}function drawRadiusLine(t,e,i,s,a){const n=t.ctx,o=e.circular,{color:r,lineWidth:l}=e;!o&&!s||!r||!l||i<0||(n.save(),n.strokeStyle=r,n.lineWidth=l,n.setLineDash(a.dash||[]),n.lineDashOffset=a.dashOffset,n.beginPath(),pathRadiusLine(t,i,o,s),n.closePath(),n.stroke(),n.restore())}function createPointLabelContext(t,e,i){return createContext(t,{label:i,index:e,type:"pointLabel"})}class RadialLinearScale extends LinearScaleBase{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ticks.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=toPadding(getTickBackdropHeight(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=isNumberFinite(t)&&!isNaN(t)?t:0,this.max=isNumberFinite(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/getTickBackdropHeight(this.options))}generateTickLabels(t){LinearScaleBase.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map(((t,e)=>{const i=callback(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""})).filter(((t,e)=>this.chart.getDataVisibility(e)))}fit(){const t=this.options;t.display&&t.pointLabels.display?fitWithPointLabels(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){const e=TAU/(this._pointLabels.length||1),i=this.options.startAngle||0;return _normalizeAngle(t*e+toRadians(i))}getDistanceFromCenterForValue(t){if(isNullOrUndef(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(isNullOrUndef(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const i=e[t];return createPointLabelContext(this.getContext(),t,i)}}getPointPosition(t,e,i=0){const s=this.getIndexAngle(t)-HALF_PI+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:i,right:s,bottom:a}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:a}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),pathRadiusLine(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:i,grid:s,border:a}=e,n=this._pointLabels.length;let o,r,l;if(e.pointLabels.display&&drawPointLabels(this,n),s.display&&this.ticks.forEach(((t,e)=>{if(0!==e||0===e&&this.min<0){r=this.getDistanceFromCenterForValue(t.value);const i=this.getContext(e),o=s.setContext(i),l=a.setContext(i);drawRadiusLine(this,o,r,n,l)}})),i.display){for(t.save(),o=n-1;o>=0;o--){const s=i.setContext(this.getPointLabelContext(o)),{color:a,lineWidth:n}=s;n&&a&&(t.lineWidth=n,t.strokeStyle=a,t.setLineDash(s.borderDash),t.lineDashOffset=s.borderDashOffset,r=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),l=this.getPointPosition(o,r),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(l.x,l.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let a,n;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach(((s,o)=>{if(0===o&&this.min>=0&&!e.reverse)return;const r=i.setContext(this.getContext(o)),l=toFont(r.font);if(a=this.getDistanceFromCenterForValue(this.ticks[o].value),r.showLabelBackdrop){t.font=l.string,n=t.measureText(s.label).width,t.fillStyle=r.backdropColor;const e=toPadding(r.backdropPadding);t.fillRect(-n/2-e.left,-a-l.size/2-e.top,n+e.width,l.size+e.height)}renderText(t,s.label,0,-a,l,{color:r.color,strokeColor:r.textStrokeColor,strokeWidth:r.textStrokeWidth})})),t.restore()}drawTitle(){}}const INTERVALS={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},UNITS=Object.keys(INTERVALS);function sorter(t,e){return t-e}function parse(t,e){if(isNullOrUndef(e))return null;const i=t._adapter,{parser:s,round:a,isoWeekday:n}=t._parseOpts;let o=e;return"function"==typeof s&&(o=s(o)),isNumberFinite(o)||(o="string"==typeof s?i.parse(o,s):i.parse(o)),null===o?null:(a&&(o="week"!==a||!isNumber(n)&&!0!==n?i.startOf(o,a):i.startOf(o,"isoWeek",n)),+o)}function determineUnitForAutoTicks(t,e,i,s){const a=UNITS.length;for(let n=UNITS.indexOf(t);n<a-1;++n){const t=INTERVALS[UNITS[n]],a=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(a*t.size))<=s)return UNITS[n]}return UNITS[a-1]}function determineUnitForFormatting(t,e,i,s,a){for(let n=UNITS.length-1;n>=UNITS.indexOf(i);n--){const i=UNITS[n];if(INTERVALS[i].common&&t._adapter.diff(a,s,i)>=e-1)return i}return UNITS[i?UNITS.indexOf(i):0]}function determineMajorUnit(t){for(let e=UNITS.indexOf(t)+1,i=UNITS.length;e<i;++e)if(INTERVALS[UNITS[e]].common)return UNITS[e]}function addTick(t,e,i){if(i){if(i.length){const{lo:s,hi:a}=_lookup(i,e);t[i[s]>=e?i[s]:i[a]]=!0}}else t[e]=!0}function setMajorTicks(t,e,i,s){const a=t._adapter,n=+a.startOf(e[0].value,s),o=e[e.length-1].value;let r,l;for(r=n;r<=o;r=+a.add(r,1,s))l=i[r],l>=0&&(e[l].major=!0);return e}function ticksFromTimestamps(t,e,i){const s=[],a={},n=e.length;let o,r;for(o=0;o<n;++o)r=e[o],a[r]=o,s.push({value:r,major:!1});return 0!==n&&i?setMajorTicks(t,s,a,i):s}class TimeScale extends Scale{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const i=t.time||(t.time={}),s=this._adapter=new adapters._date(t.adapters.date);s.init(e),mergeIf(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:parse(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:s,max:a,minDefined:n,maxDefined:o}=this.getUserBounds();function r(t){n||isNaN(t.min)||(s=Math.min(s,t.min)),o||isNaN(t.max)||(a=Math.max(a,t.max))}n&&o||(r(this._getLabelBounds()),"ticks"===t.bounds&&"labels"===t.ticks.source||r(this.getMinMax(!1))),s=isNumberFinite(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),a=isNumberFinite(a)&&!isNaN(a)?a:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,a-1),this.max=Math.max(s+1,a)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const a=this.min,n=this.max,o=_filterBetween(s,a,n);return this._unit=e.unit||(i.autoSkip?determineUnitForAutoTicks(e.minUnit,this.min,this.max,this._getLabelCapacity(a)):determineUnitForFormatting(this,o.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?determineMajorUnit(this._unit):void 0,this.initOffsets(s),t.reverse&&o.reverse(),ticksFromTimestamps(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map((t=>+t.value)))}initOffsets(t=[]){let e,i,s=0,a=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),a=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);const n=t.length<3?.5:.25;s=_limitValue(s,0,n),a=_limitValue(a,0,n),this._offsets={start:s,end:a,factor:1/(s+1+a)}}_generate(){const t=this._adapter,e=this.min,i=this.max,s=this.options,a=s.time,n=a.unit||determineUnitForAutoTicks(a.minUnit,e,i,this._getLabelCapacity(e)),o=valueOrDefault(s.ticks.stepSize,1),r="week"===n&&a.isoWeekday,l=isNumber(r)||!0===r,h={};let c,d,u=e;if(l&&(u=+t.startOf(u,"isoWeek",r)),u=+t.startOf(u,l?"day":n),t.diff(i,e,n)>1e5*o)throw new Error(e+" and "+i+" are too far apart with stepSize of "+o+" "+n);const g="data"===s.ticks.source&&this.getDataTimestamps();for(c=u,d=0;c<i;c=+t.add(c,o,n),d++)addTick(h,c,g);return c!==i&&"ticks"!==s.bounds&&1!==d||addTick(h,c,g),Object.keys(h).sort(sorter).map((t=>+t))}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const i=this.options.time.displayFormats,s=this._unit,a=e||i[s];return this._adapter.format(t,a)}_tickFormatFunction(t,e,i,s){const a=this.options,n=a.ticks.callback;if(n)return callback(n,[t,e,i],this);const o=a.time.displayFormats,r=this._unit,l=this._majorUnit,h=r&&o[r],c=l&&o[l],d=i[e],u=l&&c&&d&&d.major;return this._adapter.format(t,s||(u?c:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,s=toRadians(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(s),n=Math.sin(s),o=this._resolveTickFontOptions(0).size;return{w:i*a+o*n,h:i*n+o*a}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,a=this._tickFormatFunction(t,0,ticksFromTimestamps(this,[t],this._majorUnit),s),n=this._getLabelSize(a),o=Math.floor(this.isHorizontal()?this.width/n.w:this.height/n.h)-1;return o>0?o:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(parse(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return _arrayUnique(t.sort(sorter))}}function interpolate(t,e,i){let s,a,n,o,r=0,l=t.length-1;i?(e>=t[r].pos&&e<=t[l].pos&&({lo:r,hi:l}=_lookupByKey(t,"pos",e)),({pos:s,time:n}=t[r]),({pos:a,time:o}=t[l])):(e>=t[r].time&&e<=t[l].time&&({lo:r,hi:l}=_lookupByKey(t,"time",e)),({time:s,pos:n}=t[r]),({time:a,pos:o}=t[l]));const h=a-s;return h?n+(o-n)*(e-s)/h:n}class TimeSeriesScale extends TimeScale{static id="timeseries";static defaults=TimeScale.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=interpolate(e,this.min),this._tableRange=interpolate(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,s=[],a=[];let n,o,r,l,h;for(n=0,o=t.length;n<o;++n)l=t[n],l>=e&&l<=i&&s.push(l);if(s.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(n=0,o=s.length;n<o;++n)h=s[n+1],r=s[n-1],l=s[n],Math.round((h+r)/2)!==l&&a.push({time:l,pos:n/(o-1)});return a}_generate(){const t=this.min,e=this.max;let i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort(((t,e)=>t-e))}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(interpolate(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return interpolate(this._table,i*this._tableRange+this._minPos,!0)}}var scales=Object.freeze({__proto__:null,CategoryScale:CategoryScale,LinearScale:LinearScale,LogarithmicScale:LogarithmicScale,RadialLinearScale:RadialLinearScale,TimeScale:TimeScale,TimeSeriesScale:TimeSeriesScale});const registerables=[controllers,elements,plugins,scales];export{Animation,Animations,ArcElement,BarController,BarElement,BasePlatform,BasicPlatform,BubbleController,CategoryScale,Chart,plugin_colors as Colors,DatasetController,plugin_decimation as Decimation,DomPlatform,DoughnutController,Element,index as Filler,Interaction,plugin_legend as Legend,LineController,LineElement,LinearScale,LogarithmicScale,PieController,PointElement,PolarAreaController,RadarController,RadialLinearScale,Scale,ScatterController,plugin_subtitle as SubTitle,Ticks,TimeScale,TimeSeriesScale,plugin_title as Title,plugin_tooltip as Tooltip,adapters as _adapters,_detectPlatform,animator,controllers,defaults,elements,layouts,plugins,registerables,registry,scales};
//# sourceMappingURL=/sm/510530d8ea6c29f944aba12e20518a37ec0ac200bc1cfa362acc2d5d23137833.map