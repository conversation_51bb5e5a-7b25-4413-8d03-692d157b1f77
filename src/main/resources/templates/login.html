<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PISM-TYTO 管理系统登录</title>

    <!-- Custom Tailwind CSS -->
    <link rel="stylesheet" href="/css/tailwind-custom.css">

    <!-- 修复样式 -->
    <link rel="stylesheet" href="/css/fixes.css">

    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="/css/sweetalert2.min.css">

    <!-- jQuery -->
    <script src="/js/jquery.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="/js/sweetalert2.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .hover-lift {
            transition: all 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- 登录卡片 -->
        <div class="glass-effect rounded-2xl shadow-2xl p-8 animate-fade-in">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">管理系统</h1>
                <p class="text-gray-600">PISM-TYTO Management System</p>
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <input type="text" id="username" name="username" required
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                               placeholder="请输入用户名">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <input type="password" id="password" name="password" required
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                               placeholder="请输入密码">
                    </div>
                </div>

                <button type="submit"
                        class="w-full gradient-bg text-white py-3 px-4 rounded-lg font-medium hover-lift focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg">
                    <span class="login-text">登录</span>
                </button>
            </form>

            <!-- 消息提示 -->
            <div id="message" class="mt-4"></div>

            <!-- 默认账号提示 -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 mb-2">默认账号:</h4>
                <div class="text-xs text-blue-700">
                    <p class="cursor-pointer hover:bg-blue-100 p-2 rounded transition-colors test-account" data-username="admin" data-password="123456">
                        <span class="font-medium">用户名:</span> admin<br>
                        <span class="font-medium">密码:</span> 123456<br>
                        <span class="text-xs text-blue-600">点击此处快速填充</span>
                    </p>
                </div>
                <div class="mt-2 text-xs text-blue-600">
                    <p>💡 可在 application.yml 中修改用户名和密码</p>
                </div>
            </div>
        </div>

        <!-- 版权信息 -->
        <div class="text-center mt-8">
            <p class="text-white text-sm opacity-80">© 2024 PISM-TYTO. All rights reserved.</p>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 检查是否已经登录
            checkLoginStatus();

            // 登录表单提交
            $('#loginForm').on('submit', function(e) {
                e.preventDefault();

                const username = $('#username').val();
                const password = $('#password').val();

                if (!username || !password) {
                    showMessage('请输入用户名和密码', 'error');
                    return;
                }

                // 显示加载状态
                const $submitBtn = $('button[type="submit"]');
                const $loginText = $('.login-text');
                const originalText = $loginText.text();

                $submitBtn.prop('disabled', true);
                $loginText.html(`
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    登录中...
                `);

                $.ajax({
                    url: '/login',
                    method: 'POST',
                    data: {
                        username: username,
                        password: password
                    },
                    success: function(data) {
                        if (data.code === 200) {
                            Swal.fire({
                                title: '登录成功！',
                                text: '正在跳转到管理后台...',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false,
                                customClass: {
                                    popup: 'rounded-lg'
                                }
                            }).then(() => {
                                window.location.href = '/admin';
                            });
                        } else {
                            showMessage(data.message, 'error');
                            resetButton($submitBtn, $loginText, originalText);
                        }
                    },
                    error: function() {
                        showMessage('登录失败，请重试', 'error');
                        resetButton($submitBtn, $loginText, originalText);
                    }
                });
            });

            // 测试账号点击事件
            $('.test-account').click(function() {
                const username = $(this).data('username');
                const password = $(this).data('password');

                $('#username').val(username);
                $('#password').val(password);

                // 添加点击效果
                $(this).addClass('bg-amber-200');
                setTimeout(() => {
                    $(this).removeClass('bg-amber-200');
                }, 200);
            });
        });

        // 重置按钮状态
        function resetButton($submitBtn, $loginText, originalText) {
            $submitBtn.prop('disabled', false);
            $loginText.text(originalText);
        }

        // 检查登录状态
        function checkLoginStatus() {
            $.ajax({
                url: '/user/isLogin',
                method: 'GET',
                success: function(data) {
                    if (data.isLogin) {
                        // 已经登录，直接跳转
                        window.location.href = '/admin';
                    }
                },
                error: function() {
                    // 忽略错误，继续显示登录页面
                }
            });
        }

        // 显示消息
        function showMessage(message, type) {
            const bgClass = type === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
            const textClass = type === 'success' ? 'text-green-800' : 'text-red-800';
            const iconColor = type === 'success' ? 'text-green-400' : 'text-red-400';

            const icon = type === 'success'
                ? '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>'
                : '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>';

            $('#message').html(`
                <div class="border-l-4 ${bgClass} p-4 rounded animate-fade-in">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 ${iconColor}" fill="currentColor" viewBox="0 0 20 20">
                                ${icon}
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium ${textClass}">${message}</p>
                        </div>
                    </div>
                </div>
            `);

            // 3秒后自动隐藏
            setTimeout(() => {
                $('#message').fadeOut();
            }, 3000);
        }
    </script>
</body>
</html>
