<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PISM-TYTO 管理后台</title>

    <!-- Custom Tailwind CSS -->
    <link rel="stylesheet" href="/css/tailwind-custom.css">

    <!-- 修复样式 -->
    <link rel="stylesheet" href="/css/fixes.css">

    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="/css/sweetalert2.min.css">

    <!-- jQuery -->
    <script src="/js/jquery.min.js"></script>

    <!-- Alpine.js -->
    <script defer src="/js/alpine.min.js"></script>

    <!-- Chart.js -->
    <script src="/js/chart.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="/js/sweetalert2.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .sidebar-transition {
            transition: all 0.3s ease-in-out;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.8);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .hover-lift {
            transition: all 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body class="bg-gray-50" x-data="adminApp()">
    <div class="min-h-screen flex">
        <!-- 侧边栏 -->
        <div class="bg-white shadow-lg w-64 min-h-screen sidebar-transition" :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">PISM-TYTO</h1>
                        <p class="text-sm text-gray-500">管理后台</p>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="mt-6">
                <div class="px-3">
                    <template x-for="item in menuItems" :key="item.id">
                        <a href="#"
                           @click="setActivePage(item.id)"
                           :class="activePage === item.id ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'"
                           class="group flex items-center px-3 py-2 text-sm font-medium rounded-l-md mb-1 transition-colors duration-200">
                            <span x-html="item.icon" class="mr-3 h-5 w-5"></span>
                            <span x-text="item.name"></span>
                        </a>
                    </template>
                </div>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                        <h2 class="text-2xl font-semibold text-gray-800" x-text="getCurrentPageTitle()"></h2>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- 状态指示器 -->
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 rounded-full" :class="isOnline ? 'bg-green-400' : 'bg-red-400'"></div>
                            <span class="text-sm text-gray-600" x-text="isOnline ? '在线' : '离线'" id="loginStatus"></span>
                        </div>

                        <!-- 时间显示 -->
                        <div class="text-sm text-gray-500" x-text="currentTime" id="currentTime"></div>

                        <!-- 用户菜单 -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium" x-text="userInfo.charAt(0)"></span>
                                </div>
                                <span class="text-sm font-medium" x-text="userInfo" id="userInfo">加载中...</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="#" @click="showProfile()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    个人资料
                                </a>
                                <a href="#" @click="showSettings()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    系统设置
                                </a>
                                <hr class="my-1">
                                <a href="#" @click="logout()" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-6">
                <!-- 仪表盘页面 -->
                <div x-show="activePage === 'dashboard'" class="fade-in">
                    <!-- 统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-xl shadow-sm p-6 hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">在线用户</p>
                                    <p class="text-3xl font-bold text-blue-600" x-text="stats.onlineUsers" id="onlineUsers">0</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">总请求数</p>
                                    <p class="text-3xl font-bold text-green-600" x-text="stats.totalRequests" id="totalRequests">0</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">系统运行时间</p>
                                    <p class="text-3xl font-bold text-orange-600" x-text="stats.systemUptime" id="systemUptime">0</p>
                                </div>
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">内存使用率</p>
                                    <p class="text-3xl font-bold text-red-600" x-text="stats.memoryUsage" id="memoryUsage">0%</p>
                                </div>
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表和系统信息 -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 系统监控图表 -->
                        <div class="lg:col-span-2 bg-white rounded-xl shadow-sm p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-lg font-semibold text-gray-800">系统监控</h3>
                                <div class="flex space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        CPU
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        内存
                                    </span>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="systemChart"></canvas>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-6">系统信息</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between py-3 border-b border-gray-100">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Spring Boot</p>
                                            <p class="text-xs text-gray-500">框架版本</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-600">3.4.1</span>
                                </div>

                                <div class="flex items-center justify-between py-3 border-b border-gray-100">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Sa-Token</p>
                                            <p class="text-xs text-gray-500">权限框架</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-600">1.38.0</span>
                                </div>

                                <div class="flex items-center justify-between py-3 border-b border-gray-100">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Java</p>
                                            <p class="text-xs text-gray-500">运行环境</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-600">17</span>
                                </div>

                                <div class="flex items-center justify-between py-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">数据库</p>
                                            <p class="text-xs text-gray-500">存储方式</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-600">内存存储</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div x-show="activePage === 'users'" class="fade-in">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-2xl font-bold text-gray-800">用户管理</h3>
                        <button @click="addUser()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>添加用户</span>
                        </button>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200" id="usersTable">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">昵称</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- 数据将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- API测试页面 -->
                <div x-show="activePage === 'api'" class="fade-in">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">API测试</h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 测试按钮 -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4">快速测试</h4>
                            <div class="space-y-3">
                                <button @click="testDashboardApi()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg flex items-center space-x-3 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    <span>测试Dashboard API</span>
                                </button>

                                <button @click="getUserInfo()" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg flex items-center space-x-3 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span>获取用户信息</span>
                                </button>

                                <button @click="checkLoginStatus()" class="w-full bg-teal-600 hover:bg-teal-700 text-white px-4 py-3 rounded-lg flex items-center space-x-3 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    <span>检查登录状态</span>
                                </button>
                            </div>
                        </div>

                        <!-- 测试结果 -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4">测试结果</h4>
                            <div id="apiResult" class="bg-gray-50 rounded-lg p-4 min-h-[300px] font-mono text-sm">
                                <div class="text-gray-500 text-center py-8">
                                    点击左侧按钮开始测试...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统管理页面 -->
                <div x-show="activePage === 'system'" class="fade-in">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">系统管理</h3>

                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">系统配置</h3>
                            <p class="mt-1 text-sm text-gray-500">系统配置功能正在开发中，敬请期待...</p>
                        </div>
                    </div>
                </div>

                <!-- 日志管理页面 -->
                <div x-show="activePage === 'logs'" class="fade-in">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">日志管理</h3>

                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">系统日志</h3>
                            <p class="mt-1 text-sm text-gray-500">日志管理功能正在开发中，敬请期待...</p>
                        </div>
                    </div>
                </div>

                <!-- 应用管理页面 -->
                <div x-show="activePage === 'apps'" class="fade-in">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-2xl font-bold text-gray-800">应用管理</h3>
                        <button @click="showCreateAppModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>添加应用</span>
                        </button>
                    </div>

                    <!-- 应用列表 -->
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h4 class="text-lg font-medium text-gray-900">应用列表</h4>
                        </div>

                        <div x-show="apps.length === 0" class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无应用</h3>
                            <p class="mt-1 text-sm text-gray-500">点击上方按钮添加第一个应用</p>
                        </div>

                        <div x-show="apps.length > 0" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用信息</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用ID</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <template x-for="app in apps" :key="app.appId">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <div class="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900" x-text="app.appName"></div>
                                                        <div class="text-sm text-gray-500" x-text="app.remark || '暂无备注'"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 font-mono" x-text="app.appId"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900" x-text="formatDateTime(app.createTime)"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="app.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                    <span x-text="app.enabled ? '启用' : '禁用'"></span>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                                <button @click="viewAppKeys(app)" class="text-blue-600 hover:text-blue-900">查看密钥</button>
                                                <button @click="toggleApp(app)" :class="app.enabled ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'">
                                                    <span x-text="app.enabled ? '禁用' : '启用'"></span>
                                                </button>
                                                <button @click="deleteApp(app)" class="text-red-600 hover:text-red-900">删除</button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建应用模态框 -->
    <div x-show="showCreateAppModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showCreateAppModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                 class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showCreateAppModal = false"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div x-show="showCreateAppModal" x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">

                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">添加应用</h3>
                    <button @click="showCreateAppModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="createApp()">
                    <div class="mb-4">
                        <label for="appName" class="block text-sm font-medium text-gray-700 mb-2">应用名称</label>
                        <input type="text" id="appName" x-model="newAppName"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="请输入应用名称" required>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" @click="showCreateAppModal = false"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            取消
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            创建应用
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 查看应用密钥模态框 -->
    <div x-show="showAppKeysModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showAppKeysModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                 class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showAppKeysModal = false"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div x-show="showAppKeysModal" x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">

                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">应用密钥</h3>
                    <button @click="showAppKeysModal = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div x-show="currentApp" class="space-y-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-2">应用信息</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">应用名称:</span>
                                <span class="ml-2 font-medium" x-text="currentApp?.appName"></span>
                            </div>
                            <div>
                                <span class="text-gray-500">应用ID:</span>
                                <span class="ml-2 font-mono text-xs" x-text="currentApp?.appId"></span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 公钥1 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-medium text-gray-900">公钥 1</h4>
                                <div class="space-x-2">
                                    <button @click="copyToClipboard(currentApp?.publicKey1, '公钥1')"
                                            class="text-blue-600 hover:text-blue-800 text-sm">复制</button>
                                    <button @click="downloadKey(currentApp?.publicKey1, `${currentApp?.appName}_public_key_1.pem`)"
                                            class="text-green-600 hover:text-green-800 text-sm">下载</button>
                                </div>
                            </div>
                            <textarea readonly x-text="currentApp?.publicKey1"
                                      class="w-full h-32 text-xs font-mono bg-white border border-gray-300 rounded p-2 resize-none"></textarea>
                        </div>

                        <!-- 公钥2 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-medium text-gray-900">公钥 2</h4>
                                <div class="space-x-2">
                                    <button @click="copyToClipboard(currentApp?.publicKey2, '公钥2')"
                                            class="text-blue-600 hover:text-blue-800 text-sm">复制</button>
                                    <button @click="downloadKey(currentApp?.publicKey2, `${currentApp?.appName}_public_key_2.pem`)"
                                            class="text-green-600 hover:text-green-800 text-sm">下载</button>
                                </div>
                            </div>
                            <textarea readonly x-text="currentApp?.publicKey2"
                                      class="w-full h-32 text-xs font-mono bg-white border border-gray-300 rounded p-2 resize-none"></textarea>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">安全提示</h4>
                                <p class="text-sm text-yellow-700 mt-1">
                                    请妥善保管这些公钥，它们用于验证来自您应用的请求。私钥已安全存储在服务器中，不会显示。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end mt-6">
                    <button @click="showAppKeysModal = false"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

                <!-- 系统管理页面 -->
                <div id="system-page" class="page-content" style="display: none;">
                    <div class="ui stackable grid">
                        <div class="sixteen wide column">
                            <div class="ui huge header">
                                <i class="settings icon"></i>
                                <div class="content">系统管理</div>
                            </div>
                        </div>
                    </div>

                    <div class="ui card" style="width: 100%;">
                        <div class="content">
                            <div class="header">系统配置</div>
                        </div>
                        <div class="content">
                            <div class="ui info message">
                                <div class="header">开发中</div>
                                <p>系统配置功能正在开发中，敬请期待...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志管理页面 -->
                <div id="logs-page" class="page-content" style="display: none;">
                    <div class="ui stackable grid">
                        <div class="sixteen wide column">
                            <div class="ui huge header">
                                <i class="file alternate outline icon"></i>
                                <div class="content">日志管理</div>
                            </div>
                        </div>
                    </div>

                    <div class="ui card" style="width: 100%;">
                        <div class="content">
                            <div class="header">系统日志</div>
                        </div>
                        <div class="content">
                            <div class="ui info message">
                                <div class="header">开发中</div>
                                <p>日志管理功能正在开发中，敬请期待...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Alpine.js 数据
        function adminApp() {
            return {
                sidebarOpen: false,
                activePage: 'apps',
                isOnline: false,
                currentTime: '',
                userInfo: '加载中...',
                stats: {
                    onlineUsers: 0,
                    totalRequests: 0,
                    systemUptime: '0h',
                    memoryUsage: '0%'
                },
                menuItems: [
                    {
                        id: 'apps',
                        name: '应用管理',
                        icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg>'
                    }
                ],

                // 应用管理相关数据
                apps: [],
                showCreateAppModal: false,
                showAppKeysModal: false,
                currentApp: null,
                newAppName: '',

                init() {
                    this.updateTime();
                    setInterval(() => this.updateTime(), 1000);
                    this.checkLoginStatus();
                    this.loadUserInfo();
                    this.loadDashboardStats();
                    this.initializeChart();
                },

                setActivePage(page) {
                    this.activePage = page;
                    this.loadPageData(page);
                },

                getCurrentPageTitle() {
                    const item = this.menuItems.find(item => item.id === this.activePage);
                    return item ? item.name : '仪表盘';
                },

                updateTime() {
                    this.currentTime = new Date().toLocaleString();
                },

                loadPageData(page) {
                    if (page === 'users') {
                        this.loadUsersData();
                    } else if (page === 'apps') {
                        this.loadAppsData();
                    }
                },

                // 应用管理方法
                async loadAppsData() {
                    try {
                        const response = await fetch('/api/apps');
                        const result = await response.json();
                        if (result.code === 200) {
                            this.apps = result.data;
                        } else {
                            Swal.fire('错误', result.message, 'error');
                        }
                    } catch (error) {
                        console.error('加载应用列表失败:', error);
                        Swal.fire('错误', '加载应用列表失败', 'error');
                    }
                },

                async createApp() {
                    if (!this.newAppName.trim()) {
                        Swal.fire('提示', '请输入应用名称', 'warning');
                        return;
                    }

                    try {
                        const response = await fetch('/api/apps', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                appName: this.newAppName.trim()
                            })
                        });

                        const result = await response.json();
                        if (result.code === 200) {
                            this.showCreateAppModal = false;
                            this.newAppName = '';
                            await this.loadAppsData();

                            // 显示创建成功的信息，包含公钥
                            this.showAppCreatedSuccess(result.data);
                        } else {
                            Swal.fire('错误', result.message, 'error');
                        }
                    } catch (error) {
                        console.error('创建应用失败:', error);
                        Swal.fire('错误', '创建应用失败', 'error');
                    }
                },

                showAppCreatedSuccess(appData) {
                    Swal.fire({
                        title: '应用创建成功！',
                        html: `
                            <div class="text-left">
                                <p><strong>应用名称:</strong> ${appData.appName}</p>
                                <p><strong>应用ID:</strong> ${appData.appId}</p>
                                <p class="mt-4 mb-2"><strong>公钥已生成，请妥善保存：</strong></p>
                                <div class="text-xs">
                                    <button onclick="navigator.clipboard.writeText('${appData.publicKey1}')"
                                            class="bg-blue-500 text-white px-2 py-1 rounded text-xs mb-2">复制公钥1</button>
                                    <button onclick="navigator.clipboard.writeText('${appData.publicKey2}')"
                                            class="bg-green-500 text-white px-2 py-1 rounded text-xs mb-2 ml-2">复制公钥2</button>
                                </div>
                            </div>
                        `,
                        icon: 'success',
                        confirmButtonText: '确定'
                    });
                },

                async viewAppKeys(app) {
                    try {
                        const response = await fetch(`/api/apps/${app.appId}`);
                        const result = await response.json();
                        if (result.code === 200) {
                            // 设置数据并显示模态框
                            this.currentApp = result.data;
                            this.showAppKeysModal = true;
                        } else {
                            Swal.fire('错误', result.message, 'error');
                        }
                    } catch (error) {
                        console.error('获取应用详情失败:', error);
                        Swal.fire('错误', '获取应用详情失败', 'error');
                    }
                },

                copyToClipboard(text, keyName) {
                    navigator.clipboard.writeText(text).then(() => {
                        Swal.fire({
                            title: '复制成功',
                            text: `${keyName} 已复制到剪贴板`,
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    }).catch(() => {
                        Swal.fire('错误', '复制失败', 'error');
                    });
                },

                downloadKey(content, filename) {
                    const blob = new Blob([content], { type: 'text/plain' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                },

                async toggleApp(app) {
                    try {
                        const response = await fetch(`/api/apps/${app.appId}/toggle`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                enabled: !app.enabled
                            })
                        });

                        const result = await response.json();
                        if (result.code === 200) {
                            await this.loadAppsData();
                            Swal.fire('成功', result.message, 'success');
                        } else {
                            Swal.fire('错误', result.message, 'error');
                        }
                    } catch (error) {
                        console.error('切换应用状态失败:', error);
                        Swal.fire('错误', '操作失败', 'error');
                    }
                },

                async deleteApp(app) {
                    const result = await Swal.fire({
                        title: '确认删除',
                        text: `确定要删除应用 "${app.appName}" 吗？此操作不可恢复！`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: '删除',
                        cancelButtonText: '取消'
                    });

                    if (result.isConfirmed) {
                        try {
                            const response = await fetch(`/api/apps/${app.appId}`, {
                                method: 'DELETE'
                            });

                            const deleteResult = await response.json();
                            if (deleteResult.code === 200) {
                                await this.loadAppsData();
                                Swal.fire('删除成功', deleteResult.message, 'success');
                            } else {
                                Swal.fire('错误', deleteResult.message, 'error');
                            }
                        } catch (error) {
                            console.error('删除应用失败:', error);
                            Swal.fire('错误', '删除失败', 'error');
                        }
                    }
                },

                formatDateTime(dateTimeString) {
                    if (!dateTimeString) return '';
                    return new Date(dateTimeString).toLocaleString('zh-CN');
                }
            }
        }

        $(document).ready(function() {
            // 初始化应用
            initializeApp();
        });

        // 应用初始化
        function initializeApp() {
            // 初始化图表
            initializeChart();
        }

        // 检查登录状态
        function checkLoginStatus() {
            $.ajax({
                url: '/user/isLogin',
                method: 'GET',
                success: function(data) {
                    if (data.isLogin) {
                        // 更新Alpine.js数据
                        Alpine.store('admin').isOnline = true;
                    } else {
                        Alpine.store('admin').isOnline = false;
                        // 如果未登录，跳转到登录页
                        window.location.href = '/login';
                    }
                },
                error: function() {
                    Alpine.store('admin').isOnline = false;
                }
            });
        }

        // 加载用户信息
        function loadUserInfo() {
            $.ajax({
                url: '/user/info',
                method: 'GET',
                success: function(data) {
                    if (data.code === 200) {
                        // 更新Alpine.js数据
                        const userInfo = `${data.user.nickname} (${data.user.username})`;
                        $('#userInfo').text(userInfo);
                        // 如果有Alpine.js实例，也更新它
                        if (window.Alpine) {
                            document.querySelector('[x-data]').__x.$data.userInfo = userInfo;
                        }
                    } else {
                        $('#userInfo').text('获取用户信息失败');
                    }
                },
                error: function() {
                    $('#userInfo').text('加载失败');
                }
            });
        }

        // 加载仪表盘统计数据
        function loadDashboardStats() {
            // 模拟数据
            $('#onlineUsers').text('1');
            $('#totalRequests').text('1,234');
            $('#systemUptime').text('24h');
            $('#memoryUsage').text('65%');

            // 更新Alpine.js数据
            if (window.Alpine) {
                const app = document.querySelector('[x-data]').__x.$data;
                app.stats = {
                    onlineUsers: '1',
                    totalRequests: '1,234',
                    systemUptime: '24h',
                    memoryUsage: '65%'
                };
            }
        }

        // 初始化图表
        function initializeChart() {
            setTimeout(() => {
                const ctx = document.getElementById('systemChart');
                if (ctx) {
                    new Chart(ctx.getContext('2d'), {
                        type: 'line',
                        data: {
                            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                            datasets: [{
                                label: 'CPU使用率',
                                data: [12, 19, 3, 5, 2, 3],
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4,
                                fill: true
                            }, {
                                label: '内存使用率',
                                data: [2, 3, 20, 5, 1, 4],
                                borderColor: '#8b5cf6',
                                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    grid: {
                                        color: '#f3f4f6'
                                    }
                                },
                                x: {
                                    grid: {
                                        color: '#f3f4f6'
                                    }
                                }
                            }
                        }
                    });
                }
            }, 100);
        }

        // 加载用户数据
        function loadUsersData() {
            // 清空现有数据
            const tbody = document.querySelector('#usersTable tbody');
            if (tbody) {
                tbody.innerHTML = '';

                // 模拟用户数据
                const users = [
                    { username: 'admin', nickname: '管理员', status: 'online', lastLogin: '2024-01-15 10:30:00' },
                    { username: 'user', nickname: '普通用户', status: 'offline', lastLogin: '2024-01-14 15:20:00' },
                    { username: 'test', nickname: '测试用户', status: 'offline', lastLogin: '2024-01-13 09:15:00' }
                ];

                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';

                    const statusClass = user.status === 'online' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
                    const statusText = user.status === 'online' ? '在线' : '离线';

                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${user.username}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.nickname}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.lastLogin}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                            <button class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    `;

                    tbody.appendChild(row);
                });
            }
        }

        // API测试功能
        function testDashboardApi() {
            const resultDiv = $('#apiResult');
            resultDiv.html(`
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">测试中...</span>
                </div>
            `);

            $.ajax({
                url: '/admin/dashboard',
                method: 'GET',
                success: function(data) {
                    if (data.code === 200) {
                        resultDiv.html(`
                            <div class="border-l-4 border-green-400 bg-green-50 p-4 rounded">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-green-800">API测试成功</h3>
                                        <div class="mt-2 text-sm text-green-700">
                                            <p>${data.message}</p>
                                        </div>
                                        <div class="mt-4">
                                            <pre class="bg-gray-100 p-3 rounded text-xs overflow-x-auto">${JSON.stringify(data, null, 2)}</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                    } else {
                        resultDiv.html(`
                            <div class="border-l-4 border-red-400 bg-red-50 p-4 rounded">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">API测试失败</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <p>${data.message}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                    }
                },
                error: function() {
                    resultDiv.html(`
                        <div class="border-l-4 border-red-400 bg-red-50 p-4 rounded">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">API测试失败</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>网络错误或服务器异常</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `);
                }
            });
        }

        // 获取用户信息
        function getUserInfo() {
            const resultDiv = $('#apiResult');
            resultDiv.html(`
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">获取中...</span>
                </div>
            `);

            $.ajax({
                url: '/user/info',
                method: 'GET',
                success: function(data) {
                    if (data.code === 200) {
                        resultDiv.html(`
                            <div class="border-l-4 border-green-400 bg-green-50 p-4 rounded">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-green-800">用户信息获取成功</h3>
                                        <div class="mt-4">
                                            <pre class="bg-gray-100 p-3 rounded text-xs overflow-x-auto">${JSON.stringify(data, null, 2)}</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                    } else {
                        resultDiv.html(`
                            <div class="border-l-4 border-red-400 bg-red-50 p-4 rounded">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">获取失败</h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <p>${data.message}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                    }
                },
                error: function() {
                    resultDiv.html(`
                        <div class="border-l-4 border-red-400 bg-red-50 p-4 rounded">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">获取用户信息失败</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>网络错误或服务器异常</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `);
                }
            });
        }

        // 退出登录
        function logout() {
            Swal.fire({
                title: '确定要退出登录吗？',
                text: '退出后需要重新登录才能访问管理功能',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '确定退出',
                cancelButtonText: '取消',
                customClass: {
                    popup: 'rounded-lg'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/logout',
                        method: 'POST',
                        success: function(data) {
                            Swal.fire({
                                title: '退出成功！',
                                text: '正在跳转到登录页面...',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false,
                                customClass: {
                                    popup: 'rounded-lg'
                                }
                            }).then(() => {
                                window.location.href = '/login';
                            });
                        },
                        error: function() {
                            Swal.fire({
                                title: '退出失败',
                                text: '请重试',
                                icon: 'error',
                                customClass: {
                                    popup: 'rounded-lg'
                                }
                            });
                        }
                    });
                }
            });
        }

        // 显示个人资料
        function showProfile() {
            Swal.fire({
                title: '个人资料',
                html: '<p class="text-gray-600">个人资料管理功能开发中...</p>',
                icon: 'info',
                customClass: {
                    popup: 'rounded-lg'
                }
            });
        }

        // 显示系统设置
        function showSettings() {
            Swal.fire({
                title: '系统设置',
                html: '<p class="text-gray-600">系统设置功能开发中...</p>',
                icon: 'info',
                customClass: {
                    popup: 'rounded-lg'
                }
            });
        }

        // 添加用户
        function addUser() {
            Swal.fire({
                title: '添加用户',
                html: `
                    <div class="space-y-4 text-left">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                            <input type="text" id="newUsername" placeholder="请输入用户名"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">昵称</label>
                            <input type="text" id="newNickname" placeholder="请输入昵称"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                            <input type="password" id="newPassword" placeholder="请输入密码"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '添加',
                cancelButtonText: '取消',
                confirmButtonColor: '#10b981',
                cancelButtonColor: '#6b7280',
                customClass: {
                    popup: 'rounded-lg'
                },
                preConfirm: () => {
                    const username = document.getElementById('newUsername').value;
                    const nickname = document.getElementById('newNickname').value;
                    const password = document.getElementById('newPassword').value;

                    if (!username || !nickname || !password) {
                        Swal.showValidationMessage('请填写所有字段');
                        return false;
                    }

                    return { username, nickname, password };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 这里可以添加实际的用户添加逻辑
                    Swal.fire({
                        title: '添加成功',
                        text: '用户已添加（演示功能）',
                        icon: 'success',
                        customClass: {
                            popup: 'rounded-lg'
                        }
                    });
                }
            });
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 延迟初始化，确保Alpine.js已加载
            setTimeout(() => {
                checkLoginStatus();
                loadUserInfo();
                loadDashboardStats();
            }, 500);
        });
    </script>
</body>
</html>
    </script>
</body>
</html>
