# PISM-TYTO 配置示例文件
# 复制此文件为 application.yml 并根据需要修改配置

spring:
  application:
    name: pism-tyto

server:
  port: 8080

# Sa-Token配置
sa-token:
  token-name: satoken
  timeout: 2592000
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

# 应用配置
app:
  # 单用户登录配置
  user:
    # 用户名 - 可以修改为任意值
    username: admin
    # 密码 - 建议修改为复杂密码
    password: 123456
    # 昵称 - 显示在界面上的名称
    nickname: 系统管理员

# 配置说明：
# 1. 修改 app.user.username 来更改登录用户名
# 2. 修改 app.user.password 来更改登录密码
# 3. 修改 app.user.nickname 来更改显示的昵称
# 4. 修改后需要重启应用程序才能生效

# 安全建议：
# - 使用强密码，包含大小写字母、数字和特殊字符
# - 定期更换密码
# - 不要在生产环境中使用默认密码
# - 考虑使用环境变量来设置敏感信息

# 环境变量配置示例：
# app:
#   user:
#     username: ${APP_USERNAME:admin}
#     password: ${APP_PASSWORD:123456}
#     nickname: ${APP_NICKNAME:系统管理员}
