# PISM-TYTO 单用户配置说明

## 概述

PISM-TYTO 现在支持基于内存的单用户登录系统，用户名和密码可以通过配置文件进行自定义配置。

## 配置方式

### 1. 配置文件配置

在 `application.yml` 文件中添加以下配置：

```yaml
# 应用配置
app:
  # 单用户登录配置
  user:
    username: admin          # 用户名
    password: 123456         # 密码
    nickname: 系统管理员      # 显示昵称
```

### 2. 环境变量配置

也可以使用环境变量来配置用户信息：

```yaml
app:
  user:
    username: ${APP_USERNAME:admin}
    password: ${APP_PASSWORD:123456}
    nickname: ${APP_NICKNAME:系统管理员}
```

然后设置环境变量：
```bash
export APP_USERNAME=myuser
export APP_PASSWORD=mypassword
export APP_NICKNAME=我的管理员
```

### 3. 命令行参数配置

启动应用时可以通过命令行参数覆盖配置：

```bash
java -jar pism-tyto.jar --app.user.username=myuser --app.user.password=mypassword
```

## 配置示例

### 默认配置
```yaml
app:
  user:
    username: admin
    password: 123456
    nickname: 系统管理员
```

### 自定义配置
```yaml
app:
  user:
    username: administrator
    password: MySecurePassword123!
    nickname: 超级管理员
```

### 生产环境配置（推荐）
```yaml
app:
  user:
    username: ${ADMIN_USERNAME:admin}
    password: ${ADMIN_PASSWORD}  # 必须通过环境变量设置
    nickname: ${ADMIN_NICKNAME:系统管理员}
```

## 安全建议

### 1. 密码安全
- **不要使用默认密码** `123456` 在生产环境中
- 使用包含大小写字母、数字和特殊字符的强密码
- 密码长度至少8位，建议12位以上
- 定期更换密码

### 2. 用户名安全
- 避免使用常见的用户名如 `admin`、`administrator`、`root`
- 使用不易猜测的用户名
- 可以使用组织特定的命名规则

### 3. 配置文件安全
- 不要将包含密码的配置文件提交到版本控制系统
- 使用环境变量来存储敏感信息
- 限制配置文件的访问权限

## 功能特性

### 1. 动态配置加载
- 应用启动时自动加载配置
- 支持配置文件热更新（需重启应用）

### 2. 登录状态管理
- 基于Sa-Token的会话管理
- 支持记住登录状态
- 自动登录状态检查

### 3. 用户信息管理
- 记录最后登录时间
- 支持用户信息更新
- 密码安全存储

## API接口

### 1. 获取登录配置
```
GET /config/login
```
返回：
```json
{
  "code": 200,
  "message": "获取配置成功",
  "defaultUser": {
    "username": "admin",
    "nickname": "系统管理员"
  }
}
```

### 2. 用户登录
```
POST /login
Content-Type: application/x-www-form-urlencoded

username=admin&password=123456
```

### 3. 获取系统配置
```
GET /config/system
```
需要登录后访问。

## 故障排除

### 1. 登录失败
- 检查用户名和密码是否正确
- 查看应用日志确认配置是否正确加载
- 确认配置文件格式正确

### 2. 配置不生效
- 重启应用程序
- 检查配置文件路径和格式
- 查看启动日志确认配置加载情况

### 3. 环境变量不生效
- 确认环境变量名称正确
- 检查环境变量是否在应用启动前设置
- 使用 `echo $VARIABLE_NAME` 验证环境变量值

## 日志说明

应用启动时会输出以下日志：
```
INFO cn.com.pism.tyto.service.UserService : 初始化单用户系统，用户名: admin
```

用户登录成功时会输出：
```
INFO cn.com.pism.tyto.service.UserService : 用户登录成功: admin
```

用户登录失败时会输出：
```
WARN cn.com.pism.tyto.service.UserService : 用户登录失败: wronguser
```

## 升级说明

从多用户系统升级到单用户系统：
1. 更新配置文件，添加 `app.user` 配置
2. 重启应用程序
3. 使用配置的用户名和密码登录

## 技术实现

- **配置管理**: Spring Boot Configuration Properties
- **用户服务**: 基于内存的单例用户管理
- **会话管理**: Sa-Token框架
- **安全存储**: 密码在日志中自动脱敏
